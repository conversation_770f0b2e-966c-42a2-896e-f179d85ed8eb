import React, { createContext, useContext, useState, useEffect } from 'react'
import { authAPI } from '../lib/api'
import toast from 'react-hot-toast'

interface User {
  _id: string
  fullName: string
  phoneNumber: string
  role: string
  status: string
  isVerified: boolean
  dealerCode: string
  profilePhoto?: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (phoneNumber: string, password: string) => Promise<boolean>
  logout: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuthStatus()
  }, [])

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem('admin_token')
      const savedUser = localStorage.getItem('admin_user')
      
      if (token && savedUser) {
        // Verify token is still valid
        const response = await authAPI.verifyToken()
        if (response.success && response.user) {
          // Check if user is admin
          if (response.user.role === 'admin') {
            setUser(response.user)
          } else {
            // User is not admin, clear storage
            localStorage.removeItem('admin_token')
            localStorage.removeItem('admin_user')
            toast.error('Admin access required')
          }
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    } finally {
      setLoading(false)
    }
  }

  const login = async (phoneNumber: string, password: string): Promise<boolean> => {
    try {
      setLoading(true)
      const response = await authAPI.login(phoneNumber, password)
      
      if (response.success && response.data && response.data.user && response.data.token) {
        // Check if user is admin
        if (response.data.user.role !== 'admin') {
          toast.error('Admin access required')
          return false
        }

        // Store token and user data
        localStorage.setItem('admin_token', response.data.token)
        localStorage.setItem('admin_user', JSON.stringify(response.data.user))
        setUser(response.data.user)
        
        toast.success('Login successful!')
        return true
      } else {
        toast.error(response.message || 'Login failed')
        return false
      }
    } catch (error: any) {
      console.error('Login error:', error)
      const message = error.response?.data?.message || 'Login failed'
      toast.error(message)
      return false
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
      setUser(null)
      toast.success('Logged out successfully')
    }
  }

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
