@import "tailwindcss";

@theme {
  --color-primary-50: #e8f5ea;
  --color-primary-100: #c3e6c8;
  --color-primary-200: #9dd6a3;
  --color-primary-300: #76c67e;
  --color-primary-400: #58ba62;
  --color-primary-500: #1ca63a;
  --color-primary-600: #189533;
  --color-primary-700: #14822c;
  --color-primary-800: #106f25;
  --color-primary-900: #0a5018;

  --color-secondary-50: #fdf2ed;
  --color-secondary-100: #f9ddd0;
  --color-secondary-200: #f5c7b0;
  --color-secondary-300: #f1b090;
  --color-secondary-400: #ed9a78;
  --color-secondary-500: #df5921;
  --color-secondary-600: #c8501e;
  --color-secondary-700: #b1471a;
  --color-secondary-800: #9a3e17;
  --color-secondary-900: #7a3012;

  --color-neutral-50: #f8f9f9;
  --color-neutral-100: #f1f2f3;
  --color-neutral-200: #e4e6e7;
  --color-neutral-300: #d7dadb;
  --color-neutral-400: #cacecf;
  --color-neutral-500: #7e8689;
  --color-neutral-600: #6f7578;
  --color-neutral-700: #606467;
  --color-neutral-800: #515356;
  --color-neutral-900: #424245;

  --color-accent-50: #fefbf0;
  --color-accent-100: #fdf4d9;
  --color-accent-200: #fbedb3;
  --color-accent-300: #f9e68c;
  --color-accent-400: #f7df66;
  --color-accent-500: #d5a81a;
  --color-accent-600: #c09717;
  --color-accent-700: #ab8614;
  --color-accent-800: #967511;
  --color-accent-900: #6d540c;

  --color-success: #1ca63a;
  --color-warning: #d5a81a;
  --color-error: #df5921;
  --color-info: #7e8689;
}

/* Custom component styles */
.card {
  @apply bg-white rounded-lg shadow-sm border border-neutral-200;
}

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
}

.btn-secondary {
  @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500;
}

.btn-danger {
  @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-secondary-500;
}

.btn-success {
  @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-md {
  @apply px-4 py-2 text-sm;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.input {
  @apply block w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm placeholder-neutral-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
}

.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-primary-100 text-primary-800;
}

.badge-warning {
  @apply bg-accent-100 text-accent-800;
}

.badge-danger {
  @apply bg-secondary-100 text-secondary-800;
}

.badge-gray {
  @apply bg-neutral-100 text-neutral-800;
}

/* Accessible color variables based on COLOR_ACCESSIBILITY_GUIDE.md */
:root {
  /* Primary Green (#1ca63a) */
  --color-primary-50: #e8f5ea;
  --color-primary-100: #c3e6c8;
  --color-primary-200: #9dd6a3;
  --color-primary-300: #76c67e;
  --color-primary-400: #58ba62;
  --color-primary-500: #1ca63a;
  --color-primary-600: #189533;
  --color-primary-700: #14822c;
  --color-primary-800: #106f25;
  --color-primary-900: #0a5018;

  /* Secondary Orange (#df5921) */
  --color-secondary-500: #df5921;
  --color-secondary-600: #c8501e;

  /* Neutral Grey (#7e8689) */
  --color-neutral-500: #7e8689;
  --color-neutral-600: #6f7578;

  /* Accent Yellow (#d5a81a) */
  --color-accent-500: #d5a81a;
  --color-accent-600: #c09717;
}

.text-primary-600 {
  color: var(--color-primary-600);
}

.text-primary-900 {
  color: var(--color-primary-900);
}

.text-danger-600 {
  @apply text-secondary-600;
}

.text-danger-900 {
  @apply text-secondary-800;
}

/* Additional utility classes */
.label {
  @apply block text-sm font-medium;
}

.shadow-medium {
  @apply shadow-md;
}

/* Focus ring for primary color */
.focus\:ring-primary-500:focus {
  --tw-ring-color: var(--color-primary-500);
}

.focus\:border-primary-500:focus {
  border-color: var(--color-primary-500);
}

.bg-primary-50 {
  background-color: var(--color-primary-50);
}

.bg-primary-100 {
  background-color: var(--color-primary-100);
}

.bg-primary-600 {
  background-color: var(--color-primary-600);
}

.text-primary-700 {
  color: var(--color-primary-700);
}
