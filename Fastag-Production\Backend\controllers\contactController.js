import { validationResult } from 'express-validator';
import Contact from '../Models/Contact.js';
import User from '../Models/user.js';

// @desc    Submit contact form
// @route   POST api/contact/submit
// @access  Public
export const submitContactForm = async (req, res) => {
    try {
        // Check for validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { phoneNumber, subject, message } = req.body;

        // Create new contact form submission
        const contact = new Contact({
            phoneNumber: phoneNumber.trim(),
            subject: subject.trim(),
            message: message.trim()
        });

        await contact.save();

        res.status(201).json({
            success: true,
            message: 'Contact form submitted successfully. We will get back to you soon!',
            data: {
                id: contact._id,
                phoneNumber: contact.phoneNumber,
                subject: contact.subject,
                status: contact.status,
                createdAt: contact.createdAt
            }
        });

    } catch (error) {
        console.error('❌ Contact form submission error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to submit contact form. Please try again later.'
        });
    }
};

// @desc    Get all contact form submissions (Admin only)
// @route   GET api/contact/all
// @access  Private (Admin only)
export const getAllContacts = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            status,
            priority,
            search,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;

        // Build filter object
        const filter = {};
        if (status && status !== 'all') filter.status = status;
        if (priority && priority !== 'all') filter.priority = priority;
        if (search) {
            filter.$or = [
                { phoneNumber: { $regex: search, $options: 'i' } },
                { subject: { $regex: search, $options: 'i' } },
                { message: { $regex: search, $options: 'i' } }
            ];
        }

        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get contacts with pagination
        const contacts = await Contact.find(filter)
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit))
            .populate('assignedTo', 'fullName phoneNumber')
            .populate('resolvedBy', 'fullName phoneNumber');

        // Get total count for pagination
        const total = await Contact.countDocuments(filter);

        // Get statistics
        const stats = await Contact.getStats();

        res.status(200).json({
            success: true,
            data: {
                contacts,
                pagination: {
                    currentPage: parseInt(page),
                    totalPages: Math.ceil(total / parseInt(limit)),
                    totalContacts: total,
                    hasNext: skip + contacts.length < total,
                    hasPrev: parseInt(page) > 1
                },
                stats
            }
        });

    } catch (error) {
        console.error('❌ Get contacts error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch contact forms'
        });
    }
};

// @desc    Get single contact form by ID
// @route   GET api/contact/:id
// @access  Private (Admin only)
export const getContactById = async (req, res) => {
    try {
        const { id } = req.params;

        const contact = await Contact.findById(id)
            .populate('assignedTo', 'fullName phoneNumber role')
            .populate('resolvedBy', 'fullName phoneNumber role');

        if (!contact) {
            return res.status(404).json({
                success: false,
                message: 'Contact form not found'
            });
        }

        res.status(200).json({
            success: true,
            data: contact
        });

    } catch (error) {
        console.error('❌ Get contact by ID error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch contact form'
        });
    }
};

// @desc    Update contact form status
// @route   PUT api/contact/:id/status
// @access  Private (Admin only)
export const updateContactStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { status, priority, adminNotes, assignedTo } = req.body;
        const adminId = req.user.id;

        const contact = await Contact.findById(id);
        if (!contact) {
            return res.status(404).json({
                success: false,
                message: 'Contact form not found'
            });
        }

        // Update fields
        if (status) contact.status = status;
        if (priority) contact.priority = priority;
        if (adminNotes !== undefined) contact.adminNotes = adminNotes;
        if (assignedTo !== undefined) contact.assignedTo = assignedTo || null;

        // If marking as resolved
        if (status === 'resolved' && contact.status !== 'resolved') {
            contact.resolvedAt = new Date();
            contact.resolvedBy = adminId;
        }

        await contact.save();

        // Populate the updated contact
        await contact.populate('assignedTo', 'fullName phoneNumber');
        await contact.populate('resolvedBy', 'fullName phoneNumber');

        res.status(200).json({
            success: true,
            message: 'Contact form updated successfully',
            data: contact
        });

    } catch (error) {
        console.error('❌ Update contact status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update contact form'
        });
    }
};

// @desc    Delete contact form
// @route   DELETE api/contact/:id
// @access  Private (Admin only)
export const deleteContact = async (req, res) => {
    try {
        const { id } = req.params;

        const contact = await Contact.findById(id);
        if (!contact) {
            return res.status(404).json({
                success: false,
                message: 'Contact form not found'
            });
        }

        await Contact.findByIdAndDelete(id);

        res.status(200).json({
            success: true,
            message: 'Contact form deleted successfully'
        });

    } catch (error) {
        console.error('❌ Delete contact error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete contact form'
        });
    }
};

// @desc    Get contact form statistics
// @route   GET api/contact/stats
// @access  Private (Admin only)
export const getContactStats = async (req, res) => {
    try {
        const stats = await Contact.getStats();
        const recentContacts = await Contact.getRecent(5);

        // Get monthly trend
        const monthlyStats = await Contact.aggregate([
            {
                $match: {
                    createdAt: {
                        $gte: new Date(new Date().setMonth(new Date().getMonth() - 6))
                    }
                }
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' }
                    },
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 }
            }
        ]);

        res.status(200).json({
            success: true,
            data: {
                stats,
                recentContacts,
                monthlyTrend: monthlyStats
            }
        });

    } catch (error) {
        console.error('❌ Get contact stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch contact statistics'
        });
    }
};
