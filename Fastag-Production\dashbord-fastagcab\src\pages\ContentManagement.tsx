import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Eye, EyeOff, Image as ImageIcon } from 'lucide-react'
import Card, { Card<PERSON>eader, CardTitle, CardContent } from '@/components/ui/Card'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import Button from '@/components/ui/Button'
import ProductModal from '@/components/modals/ProductModal'
import getServerBaseUrl from '@/envConfig'
interface Banner {
  _id: string
  title: string
  description?: string
  imageUrl: string
  actionUrl?: string
  isActive: boolean
  displayOrder: number
  viewCount: number
  clickCount: number
  createdAt: string
}

interface Offer {
  _id: string
  title: string
  description: string
  imageUrl: string
  offerType: string
  discountValue?: number
  isActive: boolean
  displayOrder: number
  startDate: string
  endDate: string
  viewCount: number
  clickCount: number
  createdAt: string
}

interface Product {
  _id: string
  name: string
  description: string
  shortDescription?: string
  category: string
  brand?: string
  price: number
  originalPrice?: number
  images: Array<{
    url: string
    alt?: string
    isPrimary: boolean
  }>
  isActive: boolean
  isFeatured: boolean
  stock: number
  viewCount: number
  salesCount: number
  createdAt: string
}

type ContentType = 'banners' | 'offers' | 'products'

const ContentManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState<ContentType>('banners')
  const [banners, setBanners] = useState<Banner[]>([])
  const [offers, setOffers] = useState<Offer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Product modal state
  const [isProductModalOpen, setIsProductModalOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)

  const BackendURL = getServerBaseUrl()
  const API_BASE_URL = BackendURL;

  useEffect(() => {
    fetchContent()
  }, [activeTab])

  const fetchContent = async () => {
    setLoading(true)
    setError(null)
    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch(`${API_BASE_URL}/api/content/${activeTab}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch ${activeTab}`)
      }

      const data = await response.json()
      
      if (activeTab === 'banners') {
        setBanners(data.data?.banners || [])
      } else if (activeTab === 'offers') {
        setOffers(data.data?.offers || [])
      } else if (activeTab === 'products') {
        setProducts(data.data?.products || [])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const toggleStatus = async (id: string, type: ContentType) => {
    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch(`${API_BASE_URL}/api/content/${type}/${id}/toggle-status`, {
        method: 'PATCH',
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to toggle ${type} status`)
      }

      // Refresh the content
      fetchContent()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    }
  }

  const deleteItem = async (id: string, type: ContentType) => {
    if (!confirm(`Are you sure you want to delete this ${type.slice(0, -1)}?`)) {
      return
    }

    try {
      const token = localStorage.getItem('admin_token')
      const response = await fetch(`${API_BASE_URL}/api/content/${type}/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`Failed to delete ${type}`)
      }

      // Refresh the content
      fetchContent()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
    }
  }

  // Product modal handlers
  const handleAddProduct = () => {
    setSelectedProduct(null)
    setIsProductModalOpen(true)
  }

  const handleEditProduct = (product: Product) => {
    setSelectedProduct(product)
    setIsProductModalOpen(true)
  }

  const handleProductSaved = () => {
    fetchContent()
    setIsProductModalOpen(false)
    setSelectedProduct(null)
  }

  const handleCloseProductModal = () => {
    setIsProductModalOpen(false)
    setSelectedProduct(null)
  }

  const renderBanners = () => (
    <div className="space-y-4">
      {banners.map((banner) => (
        <Card key={banner._id} className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <img
                  src={banner.imageUrl.startsWith('http') ? banner.imageUrl : `${API_BASE_URL}${banner.imageUrl}`}
                  alt={banner.title}
                  className="w-24 h-16 object-cover rounded-lg"
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {banner.title}
                    </h3>
                    {banner.description && (
                      <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                        {banner.description}
                      </p>
                    )}
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>Views: {banner.viewCount}</span>
                      <span>Clicks: {banner.clickCount}</span>
                      <span>Order: {banner.displayOrder}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={banner.isActive ? "secondary" : "outline"}
                      size="sm"
                      onClick={() => toggleStatus(banner._id, 'banners')}
                    >
                      {banner.isActive ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => deleteItem(banner._id, 'banners')}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  const renderOffers = () => (
    <div className="space-y-4">
      {offers.map((offer) => (
        <Card key={offer._id} className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <img
                  src={offer.imageUrl.startsWith('http') ? offer.imageUrl : `${API_BASE_URL}${offer.imageUrl}`}
                  alt={offer.title}
                  className="w-24 h-16 object-cover rounded-lg"
                />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {offer.title}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {offer.description}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>Type: {offer.offerType}</span>
                      {offer.discountValue && <span>Value: {offer.discountValue}%</span>}
                      <span>Views: {offer.viewCount}</span>
                      <span>Clicks: {offer.clickCount}</span>
                    </div>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-400">
                      <span>Start: {new Date(offer.startDate).toLocaleDateString()}</span>
                      <span>End: {new Date(offer.endDate).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={offer.isActive ? "secondary" : "outline"}
                      size="sm"
                      onClick={() => toggleStatus(offer._id, 'offers')}
                    >
                      {offer.isActive ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => deleteItem(offer._id, 'offers')}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  const renderProducts = () => (
    <div className="space-y-4">
      {products.map((product) => (
        <Card key={product._id} className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                {product.images && product.images.length > 0 ? (
                  <img
                    src={product.images[0].url.startsWith('http') ? product.images[0].url : `${API_BASE_URL}${product.images[0].url}`}
                    alt={product.name}
                    className="w-24 h-24 object-cover rounded-lg"
                  />
                ) : (
                  <div className="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center">
                    <ImageIcon className="h-8 w-8 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 truncate">
                      {product.name}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {product.shortDescription || product.description}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>Category: {product.category}</span>
                      {product.brand && <span>Brand: {product.brand}</span>}
                      <span>Price: ₹{product.price.toLocaleString()}</span>
                      <span>Stock: {product.stock}</span>
                    </div>
                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-400">
                      <span>Views: {product.viewCount}</span>
                      <span>Sales: {product.salesCount}</span>
                      <div className="flex items-center space-x-2">
                        {product.isFeatured && (
                          <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                            Featured
                          </span>
                        )}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          product.isActive
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {product.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={product.isActive ? "secondary" : "outline"}
                      size="sm"
                      onClick={() => toggleStatus(product._id, 'products')}
                      title={product.isActive ? 'Disable product' : 'Enable product'}
                    >
                      {product.isActive ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditProduct(product)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => deleteItem(product._id, 'products')}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600">Manage banners, offers, and products</p>
        </div>
        <Button
          className="flex items-center space-x-2"
          onClick={activeTab === 'products' ? handleAddProduct : undefined}
        >
          <Plus className="h-4 w-4" />
          <span>Add New {activeTab.slice(0, -1)}</span>
        </Button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {(['banners', 'offers', 'products'] as ContentType[]).map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                activeTab === tab
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div>
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner />
          </div>
        ) : (
          <div>
            {activeTab === 'banners' && renderBanners()}
            {activeTab === 'offers' && renderOffers()}
            {activeTab === 'products' && renderProducts()}
          </div>
        )}

        {!loading && (
          (activeTab === 'banners' && banners.length === 0) ||
          (activeTab === 'offers' && offers.length === 0) ||
          (activeTab === 'products' && products.length === 0)
        ) && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-2">No {activeTab} found</div>
            <p className="text-gray-500">Create your first {activeTab.slice(0, -1)} to get started</p>
          </div>
        )}
      </div>

      {/* Product Modal */}
      <ProductModal
        product={selectedProduct}
        isOpen={isProductModalOpen}
        onClose={handleCloseProductModal}
        onProductSaved={handleProductSaved}
      />
    </div>
  )
}

export default ContentManagement
