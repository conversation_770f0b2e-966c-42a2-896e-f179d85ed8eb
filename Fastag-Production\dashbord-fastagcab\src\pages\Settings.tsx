import React, { useState } from 'react'
import { Save, Bell, Shield, Database, Mail, Globe } from 'lucide-react'
import Card, { CardHeader, CardTitle, CardContent } from '../components/ui/Card'
import Button from '../components/ui/Button'
import toast from 'react-hot-toast'

const Settings: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [settings, setSettings] = useState({
    // General Settings
    siteName: 'FASTAGCAB Admin',
    siteDescription: 'Admin dashboard for FASTAGCAB electrician services',
    timezone: 'Asia/Kolkata',
    language: 'en',
    
    // Notification Settings

    smsNotifications: true,
    pushNotifications: true,
    weeklyReports: true,
    
    // Security Settings
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    
    // System Settings
    maintenanceMode: false,
    debugMode: false,
    logLevel: 'info',
    backupFrequency: 'daily',
  })

  const handleSave = async () => {
    setLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success('Settings saved successfully!')
    } catch (error) {
      toast.error('Failed to save settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600">Manage system configuration and preferences</p>
        </div>
        <Button onClick={handleSave} loading={loading}>
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <Globe className="h-5 w-5 mr-2 text-gray-600" />
              <CardTitle>General Settings</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="label">Site Name</label>
              <input
                type="text"
                className="input"
                value={settings.siteName}
                onChange={(e) => handleInputChange('siteName', e.target.value)}
              />
            </div>
            
            <div>
              <label className="label">Site Description</label>
              <textarea
                className="input"
                rows={3}
                value={settings.siteDescription}
                onChange={(e) => handleInputChange('siteDescription', e.target.value)}
              />
            </div>
            
            <div>
              <label className="label">Timezone</label>
              <select
                className="input"
                value={settings.timezone}
                onChange={(e) => handleInputChange('timezone', e.target.value)}
              >
                <option value="Asia/Kolkata">Asia/Kolkata</option>
                <option value="UTC">UTC</option>
                <option value="America/New_York">America/New_York</option>
              </select>
            </div>
            
            <div>
              <label className="label">Language</label>
              <select
                className="input"
                value={settings.language}
                onChange={(e) => handleInputChange('language', e.target.value)}
              >
                <option value="en">English</option>
                <option value="hi">Hindi</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <Bell className="h-5 w-5 mr-2 text-gray-600" />
              <CardTitle>Notification Settings</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
          
            
            <div className="flex items-center justify-between">
              <div>
                <label className="label">SMS Notifications</label>
                <p className="text-sm text-gray-500">Receive notifications via SMS</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 rounded"
                checked={settings.smsNotifications}
                onChange={(e) => handleInputChange('smsNotifications', e.target.checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="label">Push Notifications</label>
                <p className="text-sm text-gray-500">Receive browser push notifications</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 rounded"
                checked={settings.pushNotifications}
                onChange={(e) => handleInputChange('pushNotifications', e.target.checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="label">Weekly Reports</label>
                <p className="text-sm text-gray-500">Receive weekly summary reports</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 rounded"
                checked={settings.weeklyReports}
                onChange={(e) => handleInputChange('weeklyReports', e.target.checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Security Settings */}
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <Shield className="h-5 w-5 mr-2 text-gray-600" />
              <CardTitle>Security Settings</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="label">Two-Factor Authentication</label>
                <p className="text-sm text-gray-500">Add an extra layer of security</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 rounded"
                checked={settings.twoFactorAuth}
                onChange={(e) => handleInputChange('twoFactorAuth', e.target.checked)}
              />
            </div>
            
            <div>
              <label className="label">Session Timeout (minutes)</label>
              <input
                type="number"
                className="input"
                value={settings.sessionTimeout}
                onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
              />
            </div>
            
            <div>
              <label className="label">Password Expiry (days)</label>
              <input
                type="number"
                className="input"
                value={settings.passwordExpiry}
                onChange={(e) => handleInputChange('passwordExpiry', parseInt(e.target.value))}
              />
            </div>
          </CardContent>
        </Card>

        {/* System Settings */}
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <Database className="h-5 w-5 mr-2 text-gray-600" />
              <CardTitle>System Settings</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="label">Maintenance Mode</label>
                <p className="text-sm text-gray-500">Put the system in maintenance mode</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 rounded"
                checked={settings.maintenanceMode}
                onChange={(e) => handleInputChange('maintenanceMode', e.target.checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="label">Debug Mode</label>
                <p className="text-sm text-gray-500">Enable debug logging</p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-primary-600 rounded"
                checked={settings.debugMode}
                onChange={(e) => handleInputChange('debugMode', e.target.checked)}
              />
            </div>
            
            <div>
              <label className="label">Log Level</label>
              <select
                className="input"
                value={settings.logLevel}
                onChange={(e) => handleInputChange('logLevel', e.target.value)}
              >
                <option value="error">Error</option>
                <option value="warn">Warning</option>
                <option value="info">Info</option>
                <option value="debug">Debug</option>
              </select>
            </div>
            
            <div>
              <label className="label">Backup Frequency</label>
              <select
                className="input"
                value={settings.backupFrequency}
                onChange={(e) => handleInputChange('backupFrequency', e.target.value)}
              >
                <option value="hourly">Hourly</option>
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Danger Zone */}
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Danger Zone</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">Reset All Settings</h4>
                <p className="text-sm text-gray-500">Reset all settings to default values</p>
              </div>
              <Button variant="danger" size="sm">
                Reset Settings
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">Clear All Data</h4>
                <p className="text-sm text-gray-500">Permanently delete all user data and logs</p>
              </div>
              <Button variant="danger" size="sm">
                Clear Data
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default Settings
