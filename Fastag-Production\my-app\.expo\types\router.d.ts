/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/contact`; params?: Router.UnknownInputParams; } | { pathname: `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/dthschemes`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/forgot-password-otp`; params?: Router.UnknownInputParams; } | { pathname: `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/new-password`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/point-history`; params?: Router.UnknownInputParams; } | { pathname: `/points`; params?: Router.UnknownInputParams; } | { pathname: `/privacy`; params?: Router.UnknownInputParams; } | { pathname: `/products`; params?: Router.UnknownInputParams; } | { pathname: `/recharge-page`; params?: Router.UnknownInputParams; } | { pathname: `/redeem`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/schemes`; params?: Router.UnknownInputParams; } | { pathname: `/terms`; params?: Router.UnknownInputParams; } | { pathname: `/test-login`; params?: Router.UnknownInputParams; } | { pathname: `/updates`; params?: Router.UnknownInputParams; } | { pathname: `/verify-otp`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/gift` | `/gift`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/contact`; params?: Router.UnknownOutputParams; } | { pathname: `/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/dthschemes`; params?: Router.UnknownOutputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownOutputParams; } | { pathname: `/forgot-password-otp`; params?: Router.UnknownOutputParams; } | { pathname: `/forgot-password`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/login`; params?: Router.UnknownOutputParams; } | { pathname: `/new-password`; params?: Router.UnknownOutputParams; } | { pathname: `/notifications`; params?: Router.UnknownOutputParams; } | { pathname: `/point-history`; params?: Router.UnknownOutputParams; } | { pathname: `/points`; params?: Router.UnknownOutputParams; } | { pathname: `/privacy`; params?: Router.UnknownOutputParams; } | { pathname: `/products`; params?: Router.UnknownOutputParams; } | { pathname: `/recharge-page`; params?: Router.UnknownOutputParams; } | { pathname: `/redeem`; params?: Router.UnknownOutputParams; } | { pathname: `/register`; params?: Router.UnknownOutputParams; } | { pathname: `/schemes`; params?: Router.UnknownOutputParams; } | { pathname: `/terms`; params?: Router.UnknownOutputParams; } | { pathname: `/test-login`; params?: Router.UnknownOutputParams; } | { pathname: `/updates`; params?: Router.UnknownOutputParams; } | { pathname: `/verify-otp`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/gift` | `/gift`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } };
      href: Router.RelativePathString | Router.ExternalPathString | `/contact${`?${string}` | `#${string}` | ''}` | `/dashboard${`?${string}` | `#${string}` | ''}` | `/dthschemes${`?${string}` | `#${string}` | ''}` | `/edit-profile${`?${string}` | `#${string}` | ''}` | `/forgot-password-otp${`?${string}` | `#${string}` | ''}` | `/forgot-password${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/login${`?${string}` | `#${string}` | ''}` | `/new-password${`?${string}` | `#${string}` | ''}` | `/notifications${`?${string}` | `#${string}` | ''}` | `/point-history${`?${string}` | `#${string}` | ''}` | `/points${`?${string}` | `#${string}` | ''}` | `/privacy${`?${string}` | `#${string}` | ''}` | `/products${`?${string}` | `#${string}` | ''}` | `/recharge-page${`?${string}` | `#${string}` | ''}` | `/redeem${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `/schemes${`?${string}` | `#${string}` | ''}` | `/terms${`?${string}` | `#${string}` | ''}` | `/test-login${`?${string}` | `#${string}` | ''}` | `/updates${`?${string}` | `#${string}` | ''}` | `/verify-otp${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/gift${`?${string}` | `#${string}` | ''}` | `/gift${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/contact`; params?: Router.UnknownInputParams; } | { pathname: `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/dthschemes`; params?: Router.UnknownInputParams; } | { pathname: `/edit-profile`; params?: Router.UnknownInputParams; } | { pathname: `/forgot-password-otp`; params?: Router.UnknownInputParams; } | { pathname: `/forgot-password`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/login`; params?: Router.UnknownInputParams; } | { pathname: `/new-password`; params?: Router.UnknownInputParams; } | { pathname: `/notifications`; params?: Router.UnknownInputParams; } | { pathname: `/point-history`; params?: Router.UnknownInputParams; } | { pathname: `/points`; params?: Router.UnknownInputParams; } | { pathname: `/privacy`; params?: Router.UnknownInputParams; } | { pathname: `/products`; params?: Router.UnknownInputParams; } | { pathname: `/recharge-page`; params?: Router.UnknownInputParams; } | { pathname: `/redeem`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/schemes`; params?: Router.UnknownInputParams; } | { pathname: `/terms`; params?: Router.UnknownInputParams; } | { pathname: `/test-login`; params?: Router.UnknownInputParams; } | { pathname: `/updates`; params?: Router.UnknownInputParams; } | { pathname: `/verify-otp`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/gift` | `/gift`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | `/+not-found${`?${string}` | `#${string}` | ''}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } };
    }
  }
}
