import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import User from './Models/user.js';

// Load environment variables
dotenv.config();

const checkAdminUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Check if user exists with the provided phone number
    const phoneNumber = '8959305283';
    const user = await User.findOne({ phoneNumber });

    if (!user) {
      console.log(`❌ No user found with phone number: ${phoneNumber}`);
      console.log('📋 Available users in database:');
      
      const allUsers = await User.find({}, 'fullName phoneNumber role').limit(10);
      allUsers.forEach((u, index) => {
        console.log(`   ${index + 1}. ${u.fullName} - ${u.phoneNumber} (${u.role})`);
      });
      
      process.exit(1);
    }

    console.log(`✅ User found: ${user.fullName}`);
    console.log(`   Phone: ${user.phoneNumber}`);
    console.log(`   Role: ${user.role}`);
    console.log(`   Status: ${user.status}`);
    console.log(`   Is Verified: ${user.isVerified}`);

    // Test password
    const testPassword = '123456789';
    const isPasswordValid = await bcrypt.compare(testPassword, user.password);
    
    console.log(`\n🔐 Password test for "${testPassword}": ${isPasswordValid ? '✅ Valid' : '❌ Invalid'}`);

    if (!isPasswordValid) {
      console.log('\n🔧 Updating password to "123456789"...');
      const hashedPassword = await bcrypt.hash(testPassword, 12);
      await User.findByIdAndUpdate(user._id, { password: hashedPassword });
      console.log('✅ Password updated successfully!');
    }

    // Ensure user is admin and approved
    if (user.role !== 'admin' || user.status !== 'approved') {
      console.log('\n🔧 Updating user role to admin and status to approved...');
      await User.findByIdAndUpdate(user._id, { role: 'admin', status: 'approved' });
      console.log('✅ User role updated to admin and status approved!');
    }

    console.log('\n🎯 Login credentials:');
    console.log(`   Phone: ${phoneNumber}`);
    console.log(`   Password: ${testPassword}`);
    console.log(`   Role: admin`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
    process.exit(0);
  }
};

checkAdminUser();
