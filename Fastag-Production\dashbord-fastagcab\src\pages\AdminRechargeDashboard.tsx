import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Phone,
  User,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Search,
  Filter,
  Eye,
  AlertCircle,
  IndianRupee,
  Smartphone
} from 'lucide-react';
import getServerBaseUrl from '@/envConfig';
import toast from 'react-hot-toast';
import Card, { CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import Button from '../components/ui/Button';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { Badge } from '../components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';
import { Textarea } from '../components/ui/textarea';

interface User {
  _id: string;
  fullName: string;
  phoneNumber: string;
}

interface Recharge {
  _id: string;
  userId: User;
  mobileNumber: string;
  operator: string;
  rechargeAmount: number;
  pointsDeducted: number;
  status: 'pending' | 'completed' | 'denied';
  rechargeDate: string;
  adminNotes?: string;
}

const AdminRechargeDashboard: React.FC = () => {
  const [recharges, setRecharges] = useState<Recharge[]>([]);
  const [filteredRecharges, setFilteredRecharges] = useState<Recharge[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRecharge, setSelectedRecharge] = useState<Recharge | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');
  const [processing, setProcessing] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'completed' | 'denied'>('all');
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    completed: 0,
    denied: 0,
    totalAmount: 0
  });

  const backendUrl = getServerBaseUrl();

  // Calculate stats from recharges
  const calculateStats = (rechargeList: Recharge[]) => {
    const stats = {
      total: rechargeList.length,
      pending: rechargeList.filter(r => r.status === 'pending').length,
      completed: rechargeList.filter(r => r.status === 'completed').length,
      denied: rechargeList.filter(r => r.status === 'denied').length,
      totalAmount: rechargeList.reduce((sum, r) => sum + r.rechargeAmount, 0)
    };
    setStats(stats);
  };

  // Filter recharges based on search and status
  const filterRecharges = (rechargeList: Recharge[]) => {
    let filtered = rechargeList;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(r => r.status === statusFilter);
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(r =>
        r.userId?.fullName?.toLowerCase().includes(term) ||
        r.mobileNumber.includes(term) ||
        r.operator.toLowerCase().includes(term) ||
        r.userId?.phoneNumber?.includes(term)
      );
    }

    setFilteredRecharges(filtered);
  };

  // Fetch recharge requests from backend
  const fetchRecharges = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('admin_token');
      const url = `${backendUrl}${backendUrl.endsWith('/') ? '' : '/'}api/users/recharge`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();
      if (data.success) {
        setRecharges(data.data.recharges);
        calculateStats(data.data.recharges);
      } else {
        toast.error(data.message || 'Failed to fetch recharges');
      }
    } catch (error) {
      console.error('Error fetching recharges:', error);
      toast.error('Failed to fetch recharges');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecharges();
  }, []);

  // Filter recharges when search term or status filter changes
  useEffect(() => {
    filterRecharges(recharges);
  }, [searchTerm, statusFilter, recharges]);

  // Update recharge status
  const handleStatusUpdate = async (id: string, status: 'completed' | 'denied', notes: string) => {
    if (processing) return;

    setProcessing(id);
    try {
      const token = localStorage.getItem('admin_token');
      const url = `${backendUrl}${backendUrl.endsWith('/') ? '' : '/'}api/users/recharge/${id}`;
      const response = await fetch(url, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          status,
          adminNotes: notes,
          processedBy: 'admin_user_id', // TODO: Get from auth context
        }),
      });

      const result = await response.json();
      if (result.success) {
        toast.success(`Recharge ${status === 'completed' ? 'approved' : 'denied'} successfully`);
        await fetchRecharges(); // Refresh data
        setShowModal(false);
        setAdminNotes('');
        setSelectedRecharge(null);
      } else {
        toast.error(result.message || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating recharge:', error);
      toast.error('Failed to update recharge status');
    } finally {
      setProcessing(null);
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'default'; // Green
      case 'denied': return 'destructive'; // Red/Orange
      case 'pending': return 'secondary'; // Yellow/Grey
      default: return 'outline';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'denied': return <XCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-neutral-900">Recharge Management</h1>
          <p className="text-neutral-600 mt-1">Manage and process user recharge requests</p>
        </div>
        <Button
          onClick={fetchRecharges}
          variant="outline"
          disabled={loading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <CreditCard className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Total Requests</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-accent-100">
                <Clock className="h-6 w-6 text-accent-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Pending</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <CheckCircle className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Completed</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.completed}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-secondary-100">
                <XCircle className="h-6 w-6 text-secondary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Denied</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.denied}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <IndianRupee className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Total Amount</p>
                <p className="text-2xl font-bold text-neutral-900">₹{stats.totalAmount.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card> */}
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
                <input
                  type="text"
                  placeholder="Search by name, phone, mobile number, or operator..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-neutral-500" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
                <option value="denied">Denied</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recharge List */}
      {loading ? (
        <Card>
          <CardContent className="p-12">
            <div className="flex flex-col items-center justify-center space-y-4">
              <LoadingSpinner size="lg" />
              <p className="text-neutral-500">Loading recharge requests...</p>
            </div>
          </CardContent>
        </Card>
      ) : filteredRecharges.length === 0 ? (
        <Card>
          <CardContent className="p-12">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertCircle className="h-12 w-12 text-neutral-400" />
              <div className="text-center">
                <p className="text-neutral-900 font-medium">No recharge requests found</p>
                <p className="text-neutral-500 mt-1">
                  {searchTerm || statusFilter !== 'all'
                    ? 'Try adjusting your search or filter criteria'
                    : 'No recharge requests have been submitted yet'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredRecharges.map((item) => (
            <div
              key={item._id}
              className="cursor-pointer"
              onClick={() => {
                setSelectedRecharge(item);
                setAdminNotes(item.adminNotes || '');
                setShowModal(true);
              }}
            >
              <Card className="hover:shadow-medium transition-shadow">
                <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-12 w-12 bg-primary-100 rounded-full flex items-center justify-center">
                        <User className="h-6 w-6 text-primary-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <p className="text-lg font-semibold text-neutral-900 truncate">
                          {item.userId?.fullName || 'Unknown User'}
                        </p>
                        <Badge variant={getStatusBadgeVariant(item.status)} className="flex items-center gap-1">
                          {getStatusIcon(item.status)}
                          {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-neutral-600">
                        <div className="flex items-center gap-1">
                          <Smartphone className="h-4 w-4" />
                          {item.mobileNumber}
                        </div>
                        <div className="flex items-center gap-1">
                          <Phone className="h-4 w-4" />
                          {item.operator}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {new Date(item.rechargeDate).toLocaleDateString('en-IN')}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="text-lg font-bold text-neutral-900">₹{item.rechargeAmount}</p>
                      <p className="text-sm text-neutral-500">{item.pointsDeducted} points</p>
                    </div>
                    <Eye className="h-5 w-5 text-neutral-400" />
                  </div>
                </div>
              </CardContent>
            </Card>
            </div>
          ))}
        </div>
      )}

      {/* Modal */}
      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Recharge Request Details
            </DialogTitle>
          </DialogHeader>

          {selectedRecharge && (
            <div className="space-y-6">
              {/* User Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h4 className="font-semibold text-neutral-900">User Information</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-neutral-500" />
                      <span className="text-sm text-neutral-600">Name:</span>
                      <span className="text-sm font-medium">{selectedRecharge.userId?.fullName}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-neutral-500" />
                      <span className="text-sm text-neutral-600">Phone:</span>
                      <span className="text-sm font-medium">{selectedRecharge.userId?.phoneNumber}</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <h4 className="font-semibold text-neutral-900">Recharge Details</h4>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-4 w-4 text-neutral-500" />
                      <span className="text-sm text-neutral-600">Mobile:</span>
                      <span className="text-sm font-medium">{selectedRecharge.mobileNumber}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-neutral-500" />
                      <span className="text-sm text-neutral-600">Operator:</span>
                      <span className="text-sm font-medium">{selectedRecharge.operator}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <IndianRupee className="h-4 w-4 text-neutral-500" />
                      <span className="text-sm text-neutral-600">Amount:</span>
                      <span className="text-sm font-medium">₹{selectedRecharge.rechargeAmount} ({selectedRecharge.pointsDeducted} pts)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-neutral-500" />
                      <span className="text-sm text-neutral-600">Date:</span>
                      <span className="text-sm font-medium">{new Date(selectedRecharge.rechargeDate).toLocaleString('en-IN')}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-neutral-600">Status:</span>
                <Badge variant={getStatusBadgeVariant(selectedRecharge.status)} className="flex items-center gap-1">
                  {getStatusIcon(selectedRecharge.status)}
                  {selectedRecharge.status.charAt(0).toUpperCase() + selectedRecharge.status.slice(1)}
                </Badge>
              </div>

              {/* Admin Notes */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-neutral-900">Admin Notes</label>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add admin notes..."
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* Action Buttons */}
              {selectedRecharge.status === 'pending' && (
                <div className="flex gap-3 pt-4 border-t">
                  <Button
                    variant="success"
                    disabled={processing === selectedRecharge._id}
                    onClick={() => handleStatusUpdate(selectedRecharge._id, 'completed', adminNotes)}
                    className="flex-1 flex items-center justify-center gap-2"
                  >
                    {processing === selectedRecharge._id ? (
                      <LoadingSpinner size="sm" />
                    ) : (
                      <CheckCircle className="h-4 w-4" />
                    )}
                    Approve Recharge
                  </Button>
                  <Button
                    variant="danger"
                    disabled={processing === selectedRecharge._id}
                    onClick={() => handleStatusUpdate(selectedRecharge._id, 'denied', adminNotes)}
                    className="flex-1 flex items-center justify-center gap-2"
                  >
                    {processing === selectedRecharge._id ? (
                      <LoadingSpinner size="sm" />
                    ) : (
                      <XCircle className="h-4 w-4" />
                    )}
                    Deny Recharge
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminRechargeDashboard;
