import * as ImageManipulator from "expo-image-manipulator";
import * as FileSystem from "expo-file-system";

/**
 * Utility functions for image processing and validation
 */

export interface ImageInfo {
  uri: string;
  width: number;
  height: number;
  size: number; // in bytes
  type: string;
}

/**
 * Get detailed information about an image file
 */
export const getImageInfo = async (uri: string): Promise<ImageInfo | null> => {
  try {
    // Get file info
    const fileInfo = await FileSystem.getInfoAsync(uri);
    if (!fileInfo.exists) {
      console.error("❌ Image file does not exist:", uri);
      return null;
    }

    // Get image dimensions using ImageManipulator
    const imageResult = await ImageManipulator.manipulateAsync(uri, [], {});

    return {
      uri: uri,
      width: imageResult.width,
      height: imageResult.height,
      size: fileInfo.size || 0,
      type: "image/jpeg", // Default assumption
    };
  } catch (error) {
    console.error("❌ Failed to get image info:", error);
    return null;
  }
};

/**
 * Compress image with progressive quality reduction if needed
 */
export const compressImageProgressive = async (
  uri: string,
  maxSizeBytes: number = 8 * 1024 * 1024, // 8MB default
  maxWidth: number = 800
): Promise<string> => {
  try {
    console.log("🔄 Starting progressive image compression:", uri);

    let currentUri = uri;
    let currentQuality = 0.8;
    let currentWidth = maxWidth;
    let attempts = 0;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
      attempts++;
      console.log(`📸 Compression attempt ${attempts}/${maxAttempts}`);

      // Compress image
      const result = await ImageManipulator.manipulateAsync(
        currentUri,
        [{ resize: { width: currentWidth } }],
        {
          compress: currentQuality,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      // Check file size
      const imageInfo = await getImageInfo(result.uri);
      if (!imageInfo) {
        console.error("❌ Could not get image info after compression");
        break;
      }

      console.log(`📏 Compressed image info:`, {
        width: imageInfo.width,
        height: imageInfo.height,
        size: `${(imageInfo.size / 1024).toFixed(1)} KB`,
        quality: currentQuality,
      });

      // If size is acceptable, return
      if (imageInfo.size <= maxSizeBytes) {
        console.log("✅ Image compression successful");
        return result.uri;
      }

      // If still too large, reduce quality and/or size more aggressively
      currentUri = result.uri;
      currentQuality = Math.max(0.3, currentQuality - 0.15); // Reduce quality
      currentWidth = Math.max(400, currentWidth - 100); // Reduce width

      console.log(`⚠️ Image still too large (${(imageInfo.size / 1024 / 1024).toFixed(2)} MB), trying more aggressive compression...`);
    }

    console.warn("⚠️ Could not compress image to desired size, returning best attempt");
    return currentUri;
  } catch (error) {
    console.error("❌ Progressive image compression failed:", error);
    return uri; // Return original if all else fails
  }
};

/**
 * Validate image before upload
 */
export const validateImage = async (uri: string): Promise<{
  isValid: boolean;
  error?: string;
  info?: ImageInfo;
}> => {
  try {
    const imageInfo = await getImageInfo(uri);
    if (!imageInfo) {
      return {
        isValid: false,
        error: "Could not read image file",
      };
    }

    // Check file size (10MB limit to match backend)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (imageInfo.size > maxSize) {
      return {
        isValid: false,
        error: `Image size (${(imageInfo.size / 1024 / 1024).toFixed(2)} MB) exceeds 10MB limit`,
        info: imageInfo,
      };
    }

    // Check dimensions (reasonable limits)
    if (imageInfo.width > 4000 || imageInfo.height > 4000) {
      return {
        isValid: false,
        error: `Image dimensions (${imageInfo.width}x${imageInfo.height}) are too large`,
        info: imageInfo,
      };
    }

    return {
      isValid: true,
      info: imageInfo,
    };
  } catch (error) {
    return {
      isValid: false,
      error: `Image validation failed: ${error.message}`,
    };
  }
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * Check if URI is a local file
 */
export const isLocalFile = (uri: string): boolean => {
  return uri.startsWith("file://") || uri.startsWith("content://");
};

/**
 * Ensure URI has proper file:// prefix
 */
export const normalizeFileUri = (uri: string): string => {
  if (!uri) return uri;
  
  if (uri.startsWith("content://") || uri.startsWith("http")) {
    return uri; // Keep as is
  }
  
  if (!uri.startsWith("file://")) {
    return `file://${uri}`;
  }
  
  return uri;
};
