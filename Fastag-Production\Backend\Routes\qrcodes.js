
import express from "express";
import QRCode from "../Models/QRCode.js";
import User from "../Models/user.js";
import PointHistory from "../Models/PointHistory.js";
import PointsService from "../services/pointsService.js";
import { authenticateToken, requireAdmin } from "../middleware/auth.js";

const router = express.Router();

// Generate random QR code
const generateRandomCode = (prefix = "FAS") => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = prefix;
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// Get all QR codes
router.get("/", async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      status,
      productSize,
      pointsRange,
      dateRange,
    } = req.query;

    const query = {};

    // Search filter
    if (search) {
      query.$or = [
        { qrCode: { $regex: search, $options: "i" } },
        { productName: { $regex: search, $options: "i" } },
      ];
    }

    // Status filter
    if (status && status !== "all") {
      query.status = status;
    }

    // Product size filter
    if (productSize && productSize !== "all") {
      query.productSize = productSize;
    }

    // Points range filter
    if (pointsRange && pointsRange !== "all") {
      if (pointsRange === "low") {
        query.points = { $lte: 15 };
      } else if (pointsRange === "medium") {
        query.points = { $gt: 15, $lte: 40 };
      } else if (pointsRange === "high") {
        query.points = { $gt: 40 };
      }
    }

    // Date range filter
    if (dateRange && dateRange !== "all") {
      const now = new Date();

      if (dateRange === "today") {
        // Today: Start of today to now
        const startOfToday = new Date(now);
        startOfToday.setHours(0, 0, 0, 0);
        query.createdAt = { $gte: startOfToday, $lte: now };
      } else if (dateRange === "week") {
        // This Week: Start of week (Sunday) to now
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay()); // Go back to Sunday
        startOfWeek.setHours(0, 0, 0, 0);
        query.createdAt = { $gte: startOfWeek, $lte: now };
      } else if (dateRange === "month") {
        // This Month: Start of month to now
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        startOfMonth.setHours(0, 0, 0, 0);
        query.createdAt = { $gte: startOfMonth, $lte: now };
      }
    }

    console.log("QR Code query:", JSON.stringify(query, null, 2));

    const qrCodes = await QRCode.find(query)
      .populate("createdBy", "firstName lastName")
      .populate("redeemedBy", "firstName lastName")
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await QRCode.countDocuments(query);

    res.json({
      qrCodes,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total,
    });
  } catch (error) {
    console.error("Get QR codes error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Create QR codes (Admin only)
router.post("/", async (req, res) => {
  try {
    console.log("QR Code generation request:", req.body);
    const { productSize, quantity } = req.body;

    if (!productSize || !quantity) {
      return res
        .status(400)
        .json({ message: "Product size and quantity are required" });
    }

    // Product options mapping
    const productOptions = {
      "0.75 mm": 5,
      "1.0 mm": 10,
      "1.5 mm": 15,
      "2.5 mm": 25,
      "4.0 mm": 40,
      "6.0 mm": 60,
      "10.0 mm": 100,
      "DDH 0.75 mm": 5,
      "DDH 1.0 mm": 5,
      "DDH 1.5 mm": 10,
      "DDH 2.5 mm": 15,
      "DDH 4.0 mm": 20,
    };

    const points = productOptions[productSize];
    if (!points) {
      return res.status(400).json({ message: "Invalid product size" });
    }

    const newCodes = [];
    for (let i = 0; i < quantity; i++) {
      let qrCode;
      let isUnique = false;

      // Ensure unique QR code
      while (!isUnique) {
        qrCode = generateRandomCode();
        const existingCode = await QRCode.findOne({ qrCode });
        if (!existingCode) {
          isUnique = true;
        }
      }

      const qrCodeData = new QRCode({
        productName: `FASTAG CAB WIRE ${productSize}`,
        points,
        value: points,
        qrCode,
        productSize,
        createdBy: req.user?.userId || null,
      });

      newCodes.push(qrCodeData);
    }

    const savedCodes = await QRCode.insertMany(newCodes);

    res.status(201).json({
      message: `Successfully generated ${quantity} QR codes`,
      qrCodes: savedCodes,
    });
  } catch (error) {
    console.error("Create QR codes error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Bulk import QR codes (Admin only)
router.post("/bulk-import", async (req, res) => {
  try {
    console.log("QR Code bulk import request:", req.body);
    const { qrCodes } = req.body;

    if (!qrCodes || !Array.isArray(qrCodes)) {
      return res.status(400).json({ message: "QR codes array is required" });
    }

    const newCodes = [];
    for (const codeData of qrCodes) {
      // Check if QR code already exists
      const existingCode = await QRCode.findOne({ qrCode: codeData.qrCode });
      if (!existingCode) {
        const qrCodeDoc = new QRCode({
          productName: codeData.productName,
          points: codeData.points,
          value: codeData.value || codeData.points,
          qrCode: codeData.qrCode,
          productSize:
            codeData.productSize ||
            codeData.productName.match(/(\d+\.?\d*\s*mm)/)?.[1] ||
            "1.0 mm",
          status: codeData.status || "Not Redeem",
          createdBy: req.user?.userId || null,
        });
        newCodes.push(qrCodeDoc);
      }
    }

    const savedCodes = await QRCode.insertMany(newCodes);

    res.status(201).json({
      message: `Successfully imported ${savedCodes.length} QR codes`,
      qrCodes: savedCodes,
    });
  } catch (error) {
    console.error("Bulk import QR codes error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Process QR code (scan and add points to user)
router.post("/process", authenticateToken, async (req, res) => {
  try {
    const { qrData } = req.body;
    const userId = req.user.id;

    if (!qrData) {
      return res.status(400).json({
        success: false,
        message: "QR code data is required"
      });
    }

    // Find the QR code
    const qrCodeDoc = await QRCode.findOne({ qrCode: qrData });
    if (!qrCodeDoc) {
      return res.status(404).json({
        success: false,
        message: "Invalid QR code"
      });
    }

    // Check if QR code is already redeemed
    if (qrCodeDoc.status === "Redeemed") {
      return res.status(400).json({
        success: false,
        message: "QR code has already been scanned"
      });
    }

    // Update QR code status
    qrCodeDoc.status = "Redeemed";
    qrCodeDoc.redeemedBy = userId;
    qrCodeDoc.redeemedAt = new Date();
    await qrCodeDoc.save();

    // Use PointsService to add points (handles both monthly and yearly)
    const pointsResult = await PointsService.addPoints(
      userId,
      qrCodeDoc.points,
      'qr_scan',
      {
        qrCode: qrCodeDoc.qrCode,
        productName: qrCodeDoc.productName,
        sourceId: qrCodeDoc._id,
        sourceModel: 'QRCode'
      }
    );

    // Add QR code to user's scanned codes
    const user = pointsResult.user;
    if (!user.scannedQRCodes.includes(qrCodeDoc._id)) {
      user.scannedQRCodes.push(qrCodeDoc._id);
      await user.save();
    }

    res.json({
      success: true,
      message: `Successfully scanned! You earned ${qrCodeDoc.points} points.`,
      points: qrCodeDoc.points,
      totalMonthlyPoints: pointsResult.newMonthlyBalance,
      totalYearlyPoints: pointsResult.newYearlyBalance,
      qrCode: qrCodeDoc,
      pointsBreakdown: {
        monthlyPoints: pointsResult.newMonthlyBalance,
        yearlyPoints: pointsResult.newYearlyBalance,
        pointsAdded: pointsResult.pointsAdded
      }
    });
  } catch (error) {
    console.error("Process QR code error:", error);
    res.status(500).json({
      success: false,
      message: "Server error"
    });
  }
});

// Redeem QR code (legacy endpoint)
router.post("/redeem", async (req, res) => {
  try {
    const { qrCode } = req.body;

    if (!qrCode) {
      return res.status(400).json({ message: "QR code is required" });
    }

    const qrCodeDoc = await QRCode.findOne({ qrCode });
    if (!qrCodeDoc) {
      return res.status(404).json({ message: "QR code not found" });
    }

    if (qrCodeDoc.status === "Redeemed") {
      return res.status(400).json({ message: "QR code already redeemed" });
    }

    qrCodeDoc.status = "Redeemed";
    qrCodeDoc.redeemedBy = req.user?.userId || null;
    qrCodeDoc.redeemedAt = new Date();

    await qrCodeDoc.save();

    res.json({
      message: "QR code redeemed successfully",
      points: qrCodeDoc.points,
      qrCode: qrCodeDoc,
    });
  } catch (error) {
    console.error("Redeem QR code error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Get QR code statistics
router.get("/stats", async (req, res) => {
  try {
    const totalCodes = await QRCode.countDocuments();
    const notRedeemed = await QRCode.countDocuments({ status: "Not Redeem" });
    const redeemed = await QRCode.countDocuments({ status: "Redeemed" });
    const totalPoints = await QRCode.aggregate([
      { $group: { _id: null, total: { $sum: "$points" } } },
    ]);

    res.json({
      totalCodes,
      notRedeemed,
      redeemed,
      totalPoints: totalPoints[0]?.total || 0,
    });
  } catch (error) {
    console.error("Get QR code stats error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Delete QR code (Admin only)
router.delete("/:id", async (req, res) => {
  try {
    const qrCode = await QRCode.findByIdAndDelete(req.params.id);
    if (!qrCode) {
      return res.status(404).json({ message: "QR code not found" });
    }
    res.json({ message: "QR code deleted successfully" });
  } catch (error) {
    console.error("Delete QR code error:", error);
    res.status(500).json({ message: "Server error" });
  }
});

// Get user's scanned QR codes (Admin only)
router.get("/user-scanned/:userId", authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { userId } = req.params;
    const {
      page = 1,
      limit = 10,
      search = '',
      productSize = 'all',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {
      redeemedBy: userId,
      status: 'Redeemed'
    };

    if (search) {
      query.$or = [
        { productName: { $regex: search, $options: 'i' } },
        { qrCode: { $regex: search, $options: 'i' } }
      ];
    }

    if (productSize !== 'all') {
      query.productSize = productSize;
    }

    // Get QR codes with pagination
    const qrCodes = await QRCode.find(query)
      .sort({ redeemedAt: sortOrder === 'desc' ? -1 : 1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select('qrCode productName productSize points value status redeemedAt createdAt');

    const total = await QRCode.countDocuments(query);

    res.json({
      success: true,
      data: {
        qrCodes,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalCount: total,
          hasNext: page < Math.ceil(total / limit),
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error("Get user scanned QR codes error:", error);
    res.status(500).json({
      success: false,
      message: "Server error"
    });
  }
});

export default router;
