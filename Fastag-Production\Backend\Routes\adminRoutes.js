import express from 'express';
import {
    getDashboardStats,
    getUserAnalytics,
    getPointsAnalytics,
    getQRCodeStats,
    bulkUpdateUserStatus,
    exportUsers,
    getSystemHealth,
    triggerMonthlyReset,
    triggerYearlyReset,
    getScheduledTasksStatus,
    getPointsSystemOverview
} from '../controllers/adminController.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// Apply admin authentication to all routes
router.use(authenticateToken);
router.use(requireAdmin);

// Dashboard Analytics Routes
// @route   GET api/admin/dashboard-stats
// @desc    Get dashboard statistics
// @access  Private (Admin only)
router.get('/dashboard-stats', getDashboardStats);

// @route   GET api/admin/user-analytics
// @desc    Get user analytics data
// @access  Private (Admin only)
router.get('/user-analytics', getUserAnalytics);

// @route   GET api/admin/points-analytics
// @desc    Get points analytics data
// @access  Private (Admin only)
router.get('/points-analytics', getPointsAnalytics);

// @route   GET api/admin/qr-stats
// @desc    Get QR code statistics
// @access  Private (Admin only)
router.get('/qr-stats', getQRCodeStats);

// Bulk Operations Routes
// @route   POST api/admin/bulk-update-status
// @desc    Bulk update user status
// @access  Private (Admin only)
router.post('/bulk-update-status', bulkUpdateUserStatus);

// Export Routes
// @route   GET api/admin/export-users
// @desc    Export users data
// @access  Private (Admin only)
router.get('/export-users', exportUsers);

// System Health
// @route   GET api/admin/system-health
// @desc    Get system health status
// @access  Private (Admin only)
router.get('/system-health', getSystemHealth);

// Points System Management Routes
// @route   POST api/admin/trigger-monthly-reset
// @desc    Manually trigger monthly points reset
// @access  Private (Admin only)
router.post('/trigger-monthly-reset', triggerMonthlyReset);

// @route   POST api/admin/trigger-yearly-reset
// @desc    Manually trigger yearly points reset check
// @access  Private (Admin only)
router.post('/trigger-yearly-reset', triggerYearlyReset);

// @route   GET api/admin/scheduled-tasks-status
// @desc    Get scheduled tasks status
// @access  Private (Admin only)
router.get('/scheduled-tasks-status', getScheduledTasksStatus);

// @route   GET api/admin/points-system-overview
// @desc    Get comprehensive points system overview
// @access  Private (Admin only)
router.get('/points-system-overview', getPointsSystemOverview);

export default router;
