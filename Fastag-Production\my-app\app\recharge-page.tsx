import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  SafeAreaView,
  StyleSheet,
  Modal,
  FlatList,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { useRouter } from 'expo-router';
import { useAuth } from '../contexts/AuthContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LinearGradient } from 'expo-linear-gradient';
import getServerBaseUrl from '@/envConfig';
const BackendURL = getServerBaseUrl();

// Operator data
const operators = [
  { id: 'airtel', name: 'Airtel', icon: '📱', color: '#E60012' },
  { id: 'jio', name: 'Jio', icon: '📶', color: '#0066CC' },
  { id: 'vi', name: '<PERSON><PERSON> (Vodafone Idea)', icon: '📞', color: '#E60012' },
  { id: 'bsnl', name: 'BSNL', icon: '📡', color: '#FF6600' },
  { id: 'mtnl', name: 'MTNL', icon: '📱', color: '#0066CC' },
  { id: 'other', name: 'Other', icon: '📋', color: '#666666' },
];

// Recharge amount options (month-based)
const rechargeAmounts = [
  { points: 500, amount: '1 Month', duration: 1, popular: true },
  // { points: 1000, amount: '2 Months', duration: 2, popular: false },
  // { points: 1500, amount: '3 Months', duration: 3, popular: true },
  // { points: 2000, amount: '4 Months', duration: 4, popular: false },
  // { points: 3000, amount: '6 Months', duration: 6, popular: true },
  // { points: 6000, amount: '12 Months', duration: 12, popular: false },
];

export default function RechargePage() {
  const { colors } = useTheme();
  const router = useRouter();
  const { user } = useAuth();

  // Use environment variable or fallback to production URL
  const backendUrl = BackendURL;
  const [mobileNumber, setMobileNumber] = useState('');
  const [operator, setOperator] = useState('');
  const [selectedOperator, setSelectedOperator] = useState(null);
  const [selectedAmount, setSelectedAmount] = useState(rechargeAmounts[0]);
  const [showOperatorModal, setShowOperatorModal] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [mobileError, setMobileError] = useState('');
  const [recentRecharges, setRecentRecharges] = useState([]);
  const [showRecentRecharges, setShowRecentRecharges] = useState(false);

  const monthlyPoints = user?.monthlyPoints || 0;

  // Validation functions
  const validateMobileNumber = (number) => {
    const mobileRegex = /^[6-9]\d{9}$/;
    if (!number.trim()) {
      return 'Mobile number is required';
    }
    if (!mobileRegex.test(number)) {
      return 'Please enter a valid 10-digit mobile number';
    }
    return '';
  };

  const handleMobileNumberChange = (text) => {
    setMobileNumber(text);
    const error = validateMobileNumber(text);
    setMobileError(error);
  };

  const handleOperatorSelect = (operatorData) => {
    setSelectedOperator(operatorData);
    setOperator(operatorData.name);
    setShowOperatorModal(false);
  };

  const handleAmountSelect = (amount) => {
    setSelectedAmount(amount);
  };

  const handleConfirm = async () => {
    const mobileValidationError = validateMobileNumber(mobileNumber);
    if (mobileValidationError) {
      setMobileError(mobileValidationError);
      Alert.alert('Validation Error', mobileValidationError);
      return;
    }

    if (!selectedOperator) {
      Alert.alert('Error', 'Please select an operator');
      return;
    }

    // Check minimum 500 points requirement for recharge
    if (monthlyPoints < 500) {
      Alert.alert('Insufficient Points', 'You need at least 500 monthly points to redeem any recharge.');
      return;
    }

    if (monthlyPoints < selectedAmount.points) {
      Alert.alert('Insufficient Points', `You need at least ${selectedAmount.points} monthly points to redeem this recharge.`);
      return;
    }

    setIsProcessing(true);
    try {
      // Get the stored token
      const storedToken = await AsyncStorage.getItem('authToken');

      if (!storedToken) {
        Alert.alert('Error', 'Authentication required. Please login again.');
        return;
      }

      // Call the recharge API
      const response = await fetch(`${backendUrl}api/users/recharge`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${storedToken}`
        },
        body: JSON.stringify({
          mobileNumber: mobileNumber.trim(),
          operator: selectedOperator.name,
          pointsToDeduct: selectedAmount.points,
          rechargeAmount: selectedAmount.amount,
          rechargeDuration: selectedAmount.duration,
          rechargeType: 'monthly_plan'
        })
      });

      const result = await response.json();

      if (result.success) {
        Alert.alert(
          'Success!',
          `${result.message}\nMobile: ${mobileNumber}\nOperator: ${selectedOperator.name}\nRecharge Plan: ${selectedAmount.amount}\nPoints Used: ${selectedAmount.points}\nRemaining Points: ${result.data.remainingPoints}`,
          [{ text: 'OK', onPress: () => router.back() }]
        );
      } else {
        Alert.alert('Error', result.message || 'Failed to process recharge. Please try again.');
      }
    } catch (error) {
      console.error('Recharge error:', error);
      Alert.alert('Error', 'Failed to process recharge. Please check your connection and try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Function to fetch recent recharges (simulated for now)
  const fetchRecentRecharges = async () => {
    try {
      // This would typically fetch from an API endpoint like api/users/recharge-history
      // For now, we'll simulate with some dummy data
      const dummyRecharges = [
        {
          id: '1',
          mobileNumber: '9876543210',
          operator: 'Airtel',
          amount: '1 Month',
          points: 500,
          status: 'completed',
          date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '2',
          mobileNumber: '9876543210',
          operator: 'Jio',
          amount: '2 Months',
          points: 1000,
          status: 'pending',
          date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];
      // setRecentRecharges(dummyRecharges);
    } catch (error) {
      console.error('Failed to fetch recent recharges:', error);
    }
  };

  // Fetch recent recharges on component mount
  React.useEffect(() => {
    fetchRecentRecharges();
  }, []);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView contentContainerStyle={styles.content} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <LinearGradient
          colors={['#f26621', '#ff8f00']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.headerGradient}
        >
          <View style={styles.headerContent}>
            {/* <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity> */}
            <Text style={styles.headerTitle}>Mobile Recharge</Text>
            <View style={styles.headerIcon}>
              <Ionicons name="phone-portrait" size={28} color="white" />
            </View>
          </View>
        </LinearGradient>

        <View style={[styles.card, { backgroundColor: colors.card }]}>
          {/* Points Display */}
          <LinearGradient
            colors={['#52b948', '#66bb6a']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.pointsGradient}
          >
            <View style={styles.pointsContent}>
              <Text style={styles.pointsLabel}>Available Points</Text>
              <Text style={styles.pointsValue}>{monthlyPoints}</Text>
            </View>
            <Ionicons name="wallet" size={32} color="rgba(255,255,255,0.8)" />
          </LinearGradient>

          {/* Recharge Amount Selection */}
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Select Recharge Amount</Text>
            <View style={styles.amountGrid}>
              {rechargeAmounts.map((amount, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.amountCard,
                    selectedAmount.points === amount.points && styles.amountCardSelected,
                    (monthlyPoints < 500 || monthlyPoints < amount.points) && styles.amountCardDisabled
                  ]}
                  onPress={() => handleAmountSelect(amount)}
                  disabled={monthlyPoints < 500 || monthlyPoints < amount.points}
                >
                  {amount.popular && (
                    <View style={styles.popularBadge}>
                      <Text style={styles.popularText}>Popular</Text>
                    </View>
                  )}
                  <Text style={[
                    styles.amountText,
                    selectedAmount.points === amount.points && styles.amountTextSelected,
                    monthlyPoints < amount.points && styles.amountTextDisabled
                  ]}>
                    {amount.amount}
                  </Text>
                  <Text style={[
                    styles.pointsText,
                    selectedAmount.points === amount.points && styles.pointsTextSelected,
                    monthlyPoints < amount.points && styles.pointsTextDisabled
                  ]}>
                    {amount.points} points
                  </Text>
                  {(monthlyPoints < 500 || monthlyPoints < amount.points) && (
                    <View style={styles.insufficientOverlay}>
                      <Ionicons name="lock-closed" size={16} color="#999" />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Mobile Number Input */}
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Mobile Number</Text>
            <View style={styles.inputWrapper}>
              <Ionicons name="phone-portrait" size={20} color="#666" style={styles.inputIcon} />
              <TextInput
                style={[styles.enhancedInput, {
                  borderColor: mobileError ? '#ff6b6b' : (colors.border || '#ddd'),
                  backgroundColor: colors.card,
                  color: colors.text
                }]}
                value={mobileNumber}
                onChangeText={handleMobileNumberChange}
                placeholder="Enter 10-digit mobile number"
                placeholderTextColor="#999"
                keyboardType="phone-pad"
                maxLength={10}
              />
            </View>
            {mobileError ? (
              <Text style={styles.errorText}>{mobileError}</Text>
            ) : null}
          </View>

          {/* Operator Selection */}
          <View style={styles.sectionContainer}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Select Operator</Text>
            <TouchableOpacity
              style={[styles.operatorSelector, {
                borderColor: colors.border || '#ddd',
                backgroundColor: colors.card
              }]}
              onPress={() => setShowOperatorModal(true)}
            >
              <View style={styles.operatorSelectorContent}>
                {selectedOperator ? (
                  <>
                    <Text style={styles.operatorEmoji}>{selectedOperator.icon}</Text>
                    <Text style={[styles.operatorName, { color: colors.text }]}>
                      {selectedOperator.name}
                    </Text>
                  </>
                ) : (
                  <Text style={styles.operatorPlaceholder}>Select your operator</Text>
                )}
              </View>
              <Ionicons name="chevron-down" size={20} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Summary Card */}
          <LinearGradient
            colors={['#f8f9fa', '#ffffff']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.summaryCard}
          >
            <View style={styles.summaryHeader}>
              <Ionicons name="receipt" size={20} color="#f26621" />
              <Text style={[styles.summaryTitle, { color: colors.text }]}>Recharge Summary</Text>
            </View>
            <View style={styles.summaryDivider} />
            <View style={styles.summaryRow}>
              <View style={styles.summaryLabelContainer}>
                <Ionicons name="time" size={16} color="#666" />
                <Text style={styles.summaryLabel}>Plan Duration:</Text>
              </View>
              <Text style={styles.summaryValue}>{selectedAmount.amount}</Text>
            </View>
            <View style={styles.summaryRow}>
              <View style={styles.summaryLabelContainer}>
                <Ionicons name="diamond" size={16} color="#666" />
                <Text style={styles.summaryLabel}>Points Required:</Text>
              </View>
              <Text style={[styles.summaryValue, { color: '#f26621' }]}>{selectedAmount.points}</Text>
            </View>
            <View style={styles.summaryRow}>
              <View style={styles.summaryLabelContainer}>
                <Ionicons name="wallet" size={16} color="#666" />
                <Text style={styles.summaryLabel}>Remaining Points:</Text>
              </View>
              <Text style={[styles.summaryValue, {
                color: monthlyPoints >= selectedAmount.points ? '#52b948' : '#ff6b6b',
                fontWeight: 'bold'
              }]}>
                {monthlyPoints - selectedAmount.points}
              </Text>
            </View>
            {monthlyPoints < 500 && (
              <View style={styles.warningContainer}>
                <Ionicons name="warning" size={16} color="#ff6b6b" />
                <Text style={styles.warningText}>
                  Minimum 500 monthly points required for recharge
                </Text>
              </View>
            )}
          </LinearGradient>

          {/* Confirm Button */}
          <LinearGradient
            colors={monthlyPoints >= 500 && monthlyPoints >= selectedAmount.points && selectedOperator && !mobileError && mobileNumber.length === 10
              ? ['#52b948', '#66bb6a']
              : ['#ccc', '#999']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={[styles.confirmButtonGradient, { opacity: isProcessing ? 0.7 : 1 }]}
          >
            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handleConfirm}
              disabled={isProcessing || monthlyPoints < 500 || monthlyPoints < selectedAmount.points || !selectedOperator || mobileError || mobileNumber.length !== 10}
            >
              <Ionicons
                name={isProcessing ? "hourglass" : "checkmark-circle"}
                size={20}
                color="white"
              />
              <Text style={styles.confirmButtonText}>
                {isProcessing ? 'Processing...' : `Confirm Recharge ${selectedAmount.amount}`}
              </Text>
            </TouchableOpacity>
          </LinearGradient>

          {/* Disclaimer */}
          <View style={styles.disclaimerCard}>
            <Ionicons name="information-circle" size={16} color="#FFA500" />
            <Text style={styles.disclaimerText}>
              Recharge will be processed within 24 hours. Points will be deducted immediately upon confirmation.
            </Text>
          </View>

          {/* Recent Recharges Section */}
          {recentRecharges.length > 0 && (
            <View style={styles.recentRechargesSection}>
              <TouchableOpacity
                style={styles.recentRechargesHeader}
                onPress={() => setShowRecentRecharges(!showRecentRecharges)}
              >
                <View style={styles.recentRechargesHeaderLeft}>
                  <Ionicons name="time" size={18} color="#f26621" />
                  <Text style={[styles.recentRechargesTitle, { color: colors.text }]}>
                    Recent Recharges
                  </Text>
                </View>
                <Ionicons
                  name={showRecentRecharges ? "chevron-up" : "chevron-down"}
                  size={18}
                  color="#666"
                />
              </TouchableOpacity>

              {showRecentRecharges && (
                <View style={styles.recentRechargesList}>
                  {recentRecharges.slice(0, 3).map((recharge, index) => (
                    <View key={recharge.id} style={styles.recentRechargeItem}>
                      <View style={styles.recentRechargeItemLeft}>
                        <View style={[
                          styles.rechargeStatusDot,
                          { backgroundColor: recharge.status === 'completed' ? '#52b948' : '#FFA500' }
                        ]} />
                        <View style={styles.recentRechargeItemContent}>
                          <Text style={[styles.recentRechargeNumber, { color: colors.text }]}>
                            {recharge.mobileNumber}
                          </Text>
                          <Text style={styles.recentRechargeDetails}>
                            {recharge.operator} • {recharge.amount}
                          </Text>
                          <Text style={styles.recentRechargeDate}>
                            {new Date(recharge.date).toLocaleDateString('en-IN', {
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </Text>
                        </View>
                      </View>
                      <View style={styles.recentRechargeItemRight}>
                        <Text style={styles.recentRechargePoints}>
                          -{recharge.points}
                        </Text>
                        <Text style={[
                          styles.recentRechargeStatus,
                          { color: recharge.status === 'completed' ? '#52b948' : '#FFA500' }
                        ]}>
                          {recharge.status.charAt(0).toUpperCase() + recharge.status.slice(1)}
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </View>
          )}
        </View>

        {/* Operator Selection Modal */}
        <Modal
          visible={showOperatorModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowOperatorModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
              <View style={styles.modalHeader}>
                <Text style={[styles.modalTitle, { color: colors.text }]}>Select Operator</Text>
                <TouchableOpacity
                  style={styles.modalCloseButton}
                  onPress={() => setShowOperatorModal(false)}
                >
                  <Ionicons name="close" size={24} color="#666" />
                </TouchableOpacity>
              </View>
              <FlatList
                data={operators}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[styles.operatorItem, { borderBottomColor: colors.border || '#eee' }]}
                    onPress={() => handleOperatorSelect(item)}
                  >
                    <Text style={styles.operatorItemEmoji}>{item.icon}</Text>
                    <Text style={[styles.operatorItemName, { color: colors.text }]}>{item.name}</Text>
                    <View style={[styles.operatorColorDot, { backgroundColor: item.color }]} />
                  </TouchableOpacity>
                )}
                showsVerticalScrollIndicator={false}
              />
            </View>
          </View>
        </Modal>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
  },
  headerGradient: {
    paddingTop: 12,
    paddingBottom: 12,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    flex: 1,
    textAlign: 'center',
  },
  headerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    margin: 16,
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 6,
  },
  pointsGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderRadius: 15,
    marginBottom: 25,
  },
  pointsContent: {
    flex: 1,
  },
  pointsLabel: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.9)',
    marginBottom: 4,
  },
  pointsValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
  },
  sectionContainer: {
    marginBottom: 25,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  amountGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  amountCard: {
    width: (Dimensions.get('window').width - 80) / 2,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  amountCardSelected: {
    borderColor: '#f26621',
    backgroundColor: '#f26621' + '10',
  },
  amountCardDisabled: {
    opacity: 0.5,
    backgroundColor: '#f0f0f0',
  },
  popularBadge: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#ff6b6b',
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  popularText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  amountText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  amountTextSelected: {
    color: '#f26621',
  },
  amountTextDisabled: {
    color: '#999',
  },
  pointsText: {
    fontSize: 12,
    color: '#666',
  },
  pointsTextSelected: {
    color: '#f26621',
  },
  pointsTextDisabled: {
    color: '#999',
  },
  insufficientOverlay: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  inputIcon: {
    position: 'absolute',
    left: 15,
    zIndex: 1,
  },
  enhancedInput: {
    flex: 1,
    borderWidth: 2,
    borderRadius: 12,
    paddingLeft: 45,
    paddingRight: 15,
    paddingVertical: 15,
    fontSize: 16,
  },
  errorText: {
    color: '#ff6b6b',
    fontSize: 12,
    marginTop: 5,
    marginLeft: 5,
  },
  operatorSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 2,
    borderRadius: 12,
    padding: 15,
  },
  operatorSelectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  operatorEmoji: {
    fontSize: 20,
    marginRight: 10,
  },
  operatorName: {
    fontSize: 16,
    fontWeight: '500',
  },
  operatorPlaceholder: {
    fontSize: 16,
    color: '#999',
  },
  summaryCard: {
    borderRadius: 15,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
  },
  summaryDivider: {
    height: 1,
    backgroundColor: '#e9ecef',
    marginBottom: 15,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
  },
  summaryValue: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff5f5',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
    borderWidth: 1,
    borderColor: '#fed7d7',
  },
  warningText: {
    fontSize: 12,
    color: '#ff6b6b',
    marginLeft: 6,
    flex: 1,
  },
  confirmButtonGradient: {
    borderRadius: 12,
    marginBottom: 15,
  },
  confirmButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  confirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  disclaimerCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#FFF3CD',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#FFEAA7',
  },
  disclaimerText: {
    flex: 1,
    fontSize: 12,
    color: '#856404',
    marginLeft: 8,
    lineHeight: 16,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  operatorItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
  },
  operatorItemEmoji: {
    fontSize: 24,
    marginRight: 15,
  },
  operatorItemName: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
  },
  operatorColorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  recentRechargesSection: {
    marginTop: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
    overflow: 'hidden',
  },
  recentRechargesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },
  recentRechargesHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  recentRechargesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  recentRechargesList: {
    padding: 10,
  },
  recentRechargeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  recentRechargeItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rechargeStatusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 10,
  },
  recentRechargeItemContent: {
    flex: 1,
  },
  recentRechargeNumber: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  recentRechargeDetails: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  recentRechargeDate: {
    fontSize: 11,
    color: '#999',
  },
  recentRechargeItemRight: {
    alignItems: 'flex-end',
  },
  recentRechargePoints: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ff6b6b',
    marginBottom: 2,
  },
  recentRechargeStatus: {
    fontSize: 11,
    fontWeight: '500',
  },
});
