import express from 'express';
import { body } from 'express-validator';
import {
    submitContactForm,
    getAllContacts,
    getContactById,
    updateContactStatus,
    deleteContact,
    getContactStats
} from '../controllers/contactController.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// Validation middleware for contact form submission
const contactFormValidation = [
    body('phoneNumber')
        .notEmpty()
        .withMessage('Phone number is required')
        .matches(/^[6-9]\d{9}$/)
        .withMessage('Please enter a valid 10-digit Indian phone number'),
    body('subject')
        .notEmpty()
        .withMessage('Subject is required')
        .isLength({ min: 3, max: 200 })
        .withMessage('Subject must be between 3 and 200 characters')
        .trim(),
    body('message')
        .notEmpty()
        .withMessage('Message is required')
        .isLength({ min: 10, max: 2000 })
        .withMessage('Message must be between 10 and 2000 characters')
        .trim()
];

// Validation middleware for status update
const statusUpdateValidation = [
    body('status')
        .optional()
        .isIn(['new', 'in-progress', 'resolved', 'closed'])
        .withMessage('Invalid status value'),
    body('priority')
        .optional()
        .isIn(['low', 'medium', 'high', 'urgent'])
        .withMessage('Invalid priority value'),
    body('adminNotes')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Admin notes cannot exceed 1000 characters')
        .trim(),
    body('assignedTo')
        .optional()
        .isMongoId()
        .withMessage('Invalid user ID for assignment')
];

// Public Routes
// Test route to verify contact routes are working
router.get('/test', (req, res) => {
    res.json({
        success: true,
        message: 'Contact routes are working!',
        timestamp: new Date().toISOString()
    });
});

// @route   POST api/contact/submit
// @desc    Submit contact form
// @access  Public
router.post('/submit', contactFormValidation, submitContactForm);

// Admin Routes (require authentication and admin role)
// @route   GET api/contact/stats
// @desc    Get contact form statistics
// @access  Private (Admin only)
router.get('/stats', authenticateToken, requireAdmin, getContactStats);

// @route   GET api/contact/all
// @desc    Get all contact form submissions with pagination and filtering
// @access  Private (Admin only)
router.get('/all', authenticateToken, requireAdmin, getAllContacts);

// @route   GET api/contact/:id
// @desc    Get single contact form by ID
// @access  Private (Admin only)
router.get('/:id', authenticateToken, requireAdmin, getContactById);

// @route   PUT api/contact/:id/status
// @desc    Update contact form status, priority, notes, or assignment
// @access  Private (Admin only)
router.put('/:id/status', authenticateToken, requireAdmin, statusUpdateValidation, updateContactStatus);

// @route   DELETE api/contact/:id
// @desc    Delete contact form
// @access  Private (Admin only)
router.delete('/:id', authenticateToken, requireAdmin, deleteContact);

export default router;
