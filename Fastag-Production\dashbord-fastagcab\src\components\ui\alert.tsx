import React from 'react';
import { clsx } from 'clsx';

interface AlertProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'destructive' | 'warning' | 'success';
}

export const Alert: React.FC<AlertProps> = ({ 
  children, 
  className, 
  variant = 'default' 
}) => {
  const baseClasses = 'relative w-full rounded-lg border p-4';
  
  const variantClasses = {
    default: 'border-neutral-200 bg-white text-neutral-900',
    destructive: 'border-secondary-200 bg-secondary-50 text-secondary-900',
    warning: 'border-accent-200 bg-accent-50 text-accent-900',
    success: 'border-primary-200 bg-primary-50 text-primary-900'
  };

  return (
    <div className={clsx(baseClasses, variantClasses[variant], className)}>
      {children}
    </div>
  );
};

interface AlertDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

export const AlertDescription: React.FC<AlertDescriptionProps> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={clsx('text-sm', className)}>
      {children}
    </div>
  );
};
