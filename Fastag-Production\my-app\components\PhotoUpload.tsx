import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
} from 'react-native';

interface PhotoUploadProps {
  title: string;
  subtitle?: string;
  value?: string;
  onPhotoSelected: (uri: string) => void;
  error?: string;
  required?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  cameraOnly?: boolean; // New prop to restrict to camera only
}

export default function PhotoUpload({
  title,
  subtitle,
  value,
  onPhotoSelected,
  error,
  required = false,
  icon = 'camera-outline',
  cameraOnly = false
}: PhotoUploadProps) {
  const [isLoading, setIsLoading] = useState(false);

  const requestCameraPermissions = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Camera permission is required to take photos.'
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      return false;
    }
  };

  const requestMediaLibraryPermissions = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Media library permission is required to select photos.'
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      return false;
    }
  };

  const pickImage = async () => {
    // If cameraOnly is true, directly open camera
    if (cameraOnly) {
      openCameraSimple();
      return;
    }

    Alert.alert(
      'Select Photo',
      'Choose how you want to select a photo',
      [
        {
          text: 'Camera',
          onPress: openCameraSimple,
        },
        {
          text: 'Gallery',
          onPress: openGallerySimple,
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const openGallerySimple = async () => {
    const hasPermission = await requestMediaLibraryPermissions();
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      console.log('🖼️ Opening gallery...');

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      console.log('🖼️ Gallery result:', result);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;
        console.log('🖼️ Selected image URI:', imageUri);
        onPhotoSelected(imageUri);
      }
    } catch (error: any) {
      console.error('🖼️ Gallery error:', error);
      Alert.alert('Error', `Failed to select photo: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const openCameraSimple = async () => {
    const hasPermission = await requestCameraPermissions();
    if (!hasPermission) return;

    setIsLoading(true);
    try {
      console.log('📷 Opening camera...');

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      console.log('📷 Camera result:', result);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const imageUri = result.assets[0].uri;
        console.log('📷 Captured image URI:', imageUri);
        onPhotoSelected(imageUri);
      }
    } catch (error: any) {
      console.error('📷 Camera error:', error);
      Alert.alert('Error', `Failed to capture photo: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const removePhoto = () => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove this photo?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => onPhotoSelected(''),
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {title}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      <TouchableOpacity
        style={[styles.uploadContainer, error ? styles.uploadError : null]}
        onPress={value ? removePhoto : pickImage}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator size="large" color="#1ca63a" />
        ) : value ? (
          <View style={styles.imageContainer}>
            <Image source={{ uri: value }} style={styles.image} />
            <View style={styles.imageOverlay}>
              <Ionicons name="trash-outline" size={24} color="#fff" />
              <Text style={styles.overlayText}>Tap to remove</Text>
            </View>
          </View>
        ) : (
          <View style={styles.placeholderContainer}>
            <Ionicons name={icon} size={40} color="#666" />
            <Text style={styles.placeholderText}>Tap to upload photo</Text>
            <Text style={styles.placeholderSubtext}>
              {cameraOnly ? 'Camera' : 'Camera or Gallery'}
            </Text>
          </View>
        )}
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  labelContainer: {
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  required: {
    color: '#df5921',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  uploadContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#e1e5e9',
    borderStyle: 'dashed',
    minHeight: 140,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  uploadError: {
    borderColor: '#df5921',
  },
  placeholderContainer: {
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
    fontWeight: '500',
  },
  placeholderSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayText: {
    color: '#fff',
    fontSize: 14,
    marginTop: 4,
    fontWeight: '500',
  },
  errorText: {
    color: '#df5921',
    fontSize: 14,
    marginTop: 4,
  },
});
