import { io, Socket } from 'socket.io-client';
import AsyncStorage from '@react-native-async-storage/async-storage';
import getServerBaseUrl from '@/envConfig';

interface PointsUpdateData {
  monthlyPoints: number;
  yearlyPoints: number;
  timestamp: number;
}

interface NotificationData {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: number;
}

type PointsUpdateCallback = (data: PointsUpdateData) => void;
type NotificationCallback = (data: NotificationData) => void;
type ConnectionCallback = (connected: boolean) => void;

class SocketService {
  private socket: Socket | null = null;
  private pointsUpdateCallbacks: Set<PointsUpdateCallback> = new Set();
  private notificationCallbacks: Set<NotificationCallback> = new Set();
  private connectionCallbacks: Set<ConnectionCallback> = new Set();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;

  constructor() {
    this.setupSocket();
  }

  private async setupSocket() {
    if (this.isConnecting || this.socket?.connected) {
      return;
    }

    this.isConnecting = true;
    const backendUrl = getServerBaseUrl();
    
    console.log('🔌 Setting up Socket.IO connection to:', backendUrl);

    try {
      this.socket = io(backendUrl, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        autoConnect: false
      });

      this.setupEventHandlers();
      await this.connect();
    } catch (error) {
      console.error('❌ Failed to setup socket:', error);
      this.isConnecting = false;
    }
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ Socket connected:', this.socket?.id);
      this.reconnectAttempts = 0;
      this.isConnecting = false;
      this.authenticateSocket();
      this.notifyConnectionCallbacks(true);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('🔌 Socket disconnected:', reason);
      this.notifyConnectionCallbacks(false);
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnection();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      this.isConnecting = false;
      this.handleReconnection();
    });

    this.socket.on('authenticated', (data) => {
      console.log('✅ Socket authenticated:', data);
    });

    this.socket.on('auth_error', (error) => {
      console.error('❌ Socket authentication error:', error);
    });

    this.socket.on('points_updated', (data: PointsUpdateData) => {
      console.log('📊 Received points update:', data);
      this.notifyPointsUpdateCallbacks(data);
    });

    this.socket.on('new_notification', (data: NotificationData) => {
      console.log('🔔 Received new notification:', data);
      this.notifyNotificationCallbacks(data);
    });

    this.socket.on('pong', (data) => {
      console.log('🏓 Received pong:', data);
    });
  }

  private async authenticateSocket() {
    if (!this.socket?.connected) return;

    try {
      const token = await AsyncStorage.getItem('authToken');
      if (token) {
        console.log('🔐 Authenticating socket with token');
        this.socket.emit('authenticate', token);
      } else {
        console.log('⚠️ No auth token found for socket authentication');
      }
    } catch (error) {
      console.error('❌ Error getting auth token for socket:', error);
    }
  }

  private handleReconnection() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.socket?.connected && !this.isConnecting) {
        this.connect();
      }
    }, delay);
  }

  private notifyPointsUpdateCallbacks(data: PointsUpdateData) {
    this.pointsUpdateCallbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('❌ Error in points update callback:', error);
      }
    });
  }

  private notifyNotificationCallbacks(data: NotificationData) {
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('❌ Error in notification callback:', error);
      }
    });
  }

  private notifyConnectionCallbacks(connected: boolean) {
    this.connectionCallbacks.forEach(callback => {
      try {
        callback(connected);
      } catch (error) {
        console.error('❌ Error in connection callback:', error);
      }
    });
  }

  // Public methods
  async connect() {
    if (this.socket?.connected || this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    
    try {
      if (!this.socket) {
        await this.setupSocket();
        return;
      }

      console.log('🔌 Connecting to socket...');
      this.socket.connect();
    } catch (error) {
      console.error('❌ Error connecting socket:', error);
      this.isConnecting = false;
    }
  }

  disconnect() {
    if (this.socket) {
      console.log('🔌 Disconnecting socket...');
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  // Callback management
  onPointsUpdate(callback: PointsUpdateCallback) {
    this.pointsUpdateCallbacks.add(callback);
    return () => this.pointsUpdateCallbacks.delete(callback);
  }

  onNotification(callback: NotificationCallback) {
    this.notificationCallbacks.add(callback);
    return () => this.notificationCallbacks.delete(callback);
  }

  onConnectionChange(callback: ConnectionCallback) {
    this.connectionCallbacks.add(callback);
    return () => this.connectionCallbacks.delete(callback);
  }

  // Utility methods
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  ping() {
    if (this.socket?.connected) {
      this.socket.emit('ping');
    }
  }

  // Re-authenticate after token change
  async reAuthenticate() {
    if (this.socket?.connected) {
      await this.authenticateSocket();
    }
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
