#!/usr/bin/env node

console.log('🚀 Starting FastagCab Backend Server...');
console.log('📁 Current directory:', process.cwd());
console.log('🔧 Node version:', process.version);

// Check if all required dependencies are installed
try {
  console.log('📦 Checking dependencies...');
  
  // Import the main server file
  import('./index.js')
    .then(() => {
      console.log('✅ Server started successfully!');
    })
    .catch((error) => {
      console.error('❌ Failed to start server:', error.message);
      console.error('📋 Full error:', error);
      
      if (error.code === 'ERR_MODULE_NOT_FOUND') {
        console.log('\n💡 Missing dependency detected!');
        console.log('🔧 Please run: npm install');
        console.log('📦 Then try starting the server again');
      }
      
      process.exit(1);
    });
    
} catch (error) {
  console.error('❌ Critical error:', error);
  process.exit(1);
}
