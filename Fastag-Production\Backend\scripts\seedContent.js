import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Banner from '../Models/Banner.js';
import Offer from '../Models/Offer.js';
import Product from '../Models/Product.js';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/fastagcab');
    console.log('MongoDB connected successfully');
  } catch (error) { 
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};
 
// Sample banners data
const sampleBanners = [
  {
    title: 'Welcome to FASTAGCAB',
    description: 'Your trusted partner for electrical solutions',
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=400&fit=crop&crop=center&auto=format&q=80',
    actionUrl: null,
    isActive: true,
    displayOrder: 1
  },
  {
    title: 'Premium Quality Products',
    description: 'Discover our range of high-quality electrical products',
    imageUrl: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=800&h=400&fit=crop&crop=center&auto=format&q=80',
    actionUrl: null,
    isActive: true,
    displayOrder: 2
  },
  {
    title: 'Expert Support',
    description: 'Get professional support from our expert team',
    imageUrl: 'https://images.unsplash.com/photo-1556740758-90de374c12ad?w=800&h=400&fit=crop&crop=center&auto=format&q=80',
    actionUrl: null,
    isActive: true,
    displayOrder: 3
  },
  {
    title: 'Fast Delivery',
    description: 'Quick and reliable delivery across India',
    imageUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=400&fit=crop&crop=center&auto=format&q=80',
    actionUrl: null,
    isActive: true,
    displayOrder: 4
  }
];

// Sample offers data
const sampleOffers = [
  {
    title: 'New Year Special',
    description: 'Get up to 50% off on all electrical products. Limited time offer!',
    imageUrl: 'https://images.unsplash.com/photo-1607083206869-4c7672e72a8a?w=400&h=300&fit=crop&crop=center&auto=format&q=80',
    offerType: 'percentage',
    discountValue: 50,
    minimumPurchase: 1000,
    maximumDiscount: 5000,
    isActive: true,
    displayOrder: 1,
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    usageLimit: 1000,
    termsAndConditions: 'Valid for new customers only. Cannot be combined with other offers.'
  },
  {
    title: 'Bulk Purchase Discount',
    description: 'Special discount for bulk purchases above ₹10,000',
    imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop&crop=center&auto=format&q=80',
    offerType: 'percentage',
    discountValue: 25,
    minimumPurchase: 10000,
    maximumDiscount: 10000,
    isActive: true,
    displayOrder: 2,
    startDate: new Date(),
    endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
    usageLimit: 500,
    termsAndConditions: 'Valid for bulk orders only. Minimum order value ₹10,000.'
  },
  {
    title: 'Free Shipping',
    description: 'Free shipping on orders above ₹2,000',
    imageUrl: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=300&fit=crop&crop=center&auto=format&q=80',
    offerType: 'free_shipping',
    discountValue: 0,
    minimumPurchase: 2000,
    isActive: true,
    displayOrder: 3,
    startDate: new Date(),
    endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days from now
    termsAndConditions: 'Free shipping applicable on orders above ₹2,000.'
  }
];

// Sample products data
const sampleProducts = [
  {
    name: 'LED Bulb 9W',
    description: 'Energy-efficient LED bulb with 9W power consumption. Perfect for home and office use with long-lasting performance.',
    shortDescription: 'Energy-efficient 9W LED bulb for home and office',
    category: 'Lighting',
    subcategory: 'LED Bulbs',
    brand: 'FASTAGCAB',
    model: 'LED-9W-001',
    sku: 'FTC-LED-9W-001',
    price: 299,
    originalPrice: 399,
    currency: 'INR',
    stock: 500,
    lowStockThreshold: 50,
    images: [
      {
        url: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center&auto=format&q=80',
        alt: 'LED Bulb 9W',
        isPrimary: true
      }
    ],
    specifications: [
      { name: 'Power', value: '9W' },
      { name: 'Voltage', value: '220-240V' },
      { name: 'Color Temperature', value: '6500K' },
      { name: 'Lumens', value: '900 lm' },
      { name: 'Base Type', value: 'B22' }
    ],
    features: [
      'Energy efficient',
      'Long lasting - up to 25,000 hours',
      'Instant on',
      'Cool white light',
      'Mercury free'
    ],
    tags: ['led', 'bulb', 'energy-efficient', 'lighting'],
    rating: { average: 4.5, count: 125 },
    isActive: true,
    isFeatured: true,
    displayOrder: 1,
    weight: { value: 0.1, unit: 'kg' },
    dimensions: { length: 10, width: 6, height: 6, unit: 'cm' },
    seoTitle: 'LED Bulb 9W - Energy Efficient Lighting Solution',
    seoDescription: 'Buy high-quality 9W LED bulb with long-lasting performance. Energy efficient and eco-friendly lighting solution.'
  },
  {
    name: 'Ceiling Fan 48 Inch',
    description: 'High-performance ceiling fan with 48-inch sweep. Features aerodynamic design for maximum air delivery with minimal noise.',
    shortDescription: 'High-performance 48-inch ceiling fan with aerodynamic design',
    category: 'Fans',
    subcategory: 'Ceiling Fans',
    brand: 'FASTAGCAB',
    model: 'CF-48-PRO',
    sku: 'FTC-CF-48-PRO',
    price: 2499,
    originalPrice: 2999,
    currency: 'INR',
    stock: 150,
    lowStockThreshold: 20,
    images: [
      {
        url: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=400&fit=crop&crop=center&auto=format&q=80',
        alt: 'Ceiling Fan 48 Inch',
        isPrimary: true
      }
    ],
    specifications: [
      { name: 'Sweep', value: '48 inches' },
      { name: 'Power', value: '75W' },
      { name: 'Speed', value: '3 Speed' },
      { name: 'RPM', value: '350 RPM' },
      { name: 'Material', value: 'Metal' }
    ],
    features: [
      'Aerodynamic blade design',
      'High air delivery',
      'Low noise operation',
      '3-speed control',
      'Rust resistant finish'
    ],
    tags: ['ceiling-fan', 'fan', '48-inch', 'home-appliance'],
    rating: { average: 4.3, count: 89 },
    isActive: true,
    isFeatured: true,
    displayOrder: 2,
    weight: { value: 8.5, unit: 'kg' },
    dimensions: { length: 122, width: 122, height: 35, unit: 'cm' },
    seoTitle: '48 Inch Ceiling Fan - High Performance & Low Noise',
    seoDescription: 'Premium 48-inch ceiling fan with aerodynamic design. High air delivery with low noise operation.'
  },
  {
    name: 'Extension Cord 5 Meter',
    description: 'Heavy-duty extension cord with 5-meter length. Features multiple sockets and surge protection for safe electrical connections.',
    shortDescription: 'Heavy-duty 5-meter extension cord with surge protection',
    category: 'Electrical Accessories',
    subcategory: 'Extension Cords',
    brand: 'FASTAGCAB',
    model: 'EC-5M-HD',
    sku: 'FTC-EC-5M-HD',
    price: 899,
    originalPrice: 1199,
    currency: 'INR',
    stock: 200,
    lowStockThreshold: 30,
    images: [
      {
        url: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=400&fit=crop&crop=center&auto=format&q=80',
        alt: 'Extension Cord 5 Meter',
        isPrimary: true
      }
    ],
    specifications: [
      { name: 'Length', value: '5 meters' },
      { name: 'Sockets', value: '4 sockets' },
      { name: 'Current Rating', value: '16A' },
      { name: 'Voltage', value: '240V' },
      { name: 'Wire Gauge', value: '2.5 sq mm' }
    ],
    features: [
      'Heavy-duty construction',
      'Surge protection',
      'Multiple sockets',
      'Flexible cable',
      'Safety certified'
    ],
    tags: ['extension-cord', 'electrical', 'safety', 'surge-protection'],
    rating: { average: 4.2, count: 67 },
    isActive: true,
    isFeatured: false,
    displayOrder: 3,
    weight: { value: 1.2, unit: 'kg' },
    dimensions: { length: 500, width: 8, height: 3, unit: 'cm' },
    seoTitle: '5 Meter Extension Cord - Heavy Duty with Surge Protection',
    seoDescription: 'Reliable 5-meter extension cord with multiple sockets and surge protection. Perfect for home and office use.'
  }
];

// Seed function
const seedContent = async () => {
  try {
    console.log('🌱 Starting content seeding...');

    // Clear existing data
    await Banner.deleteMany({});
    await Offer.deleteMany({});
    await Product.deleteMany({});
    console.log('✅ Cleared existing content');

    // Insert banners
    const banners = await Banner.insertMany(sampleBanners);
    console.log(`✅ Inserted ${banners.length} banners`);

    // Insert offers
    const offers = await Offer.insertMany(sampleOffers);
    console.log(`✅ Inserted ${offers.length} offers`);

    // Insert products
    const products = await Product.insertMany(sampleProducts);
    console.log(`✅ Inserted ${products.length} products`);

    console.log('🎉 Content seeding completed successfully!');
    
    // Display summary
    console.log('\n📊 Summary:');
    console.log(`- Banners: ${banners.length}`);
    console.log(`- Offers: ${offers.length}`);
    console.log(`- Products: ${products.length}`);
    
  } catch (error) {
    console.error('❌ Error seeding content:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run the seeder
const runSeeder = async () => {
  await connectDB();
  await seedContent();
};

// Execute if run directly
console.log('🌱 Starting seeder script...');
runSeeder();

export default seedContent;
