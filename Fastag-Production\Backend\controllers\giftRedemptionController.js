import { validationResult } from 'express-validator';
import GiftRedemption from '../Models/GiftRedemption.js';
import User from '../Models/user.js';
import Notification from '../Models/Notification.js';
import PointHistory from '../Models/PointHistory.js';
import PointsService from '../services/pointsService.js';

// @desc    Create a new gift redemption request
// @route   POST api/gifts/redeem
// @access  Private
export const createRedemption = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { productId, productName, productImage, pointsRequired } = req.body;

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Only check yearly points for gift redemption
    if (user.yearlyPoints < pointsRequired) {
      return res.status(400).json({
        success: false,
        message: `Insufficient yearly points. You have ${user.yearlyPoints} yearly points but need ${pointsRequired} points.`
      });
    }


    // Store points before deduction for redemption record
    const userMonthlyPointsAtRedemption = user.monthlyPoints;
    const userYearlyPointsAtRedemption = user.yearlyPoints;

    // Check if user has sufficient yearly points for gift redemption
    if (!user.hasSufficientPoints(pointsRequired, 'gift', 'gift')) {
      return res.status(400).json({
        success: false,
        message: `Insufficient yearly points for gift redemption. Required: ${pointsRequired}, Available: ${user.yearlyPoints}`
      });
    }

    // Note: Points will be deducted when admin approves the redemption request
    // This prevents points from being deducted for pending requests

    // Create redemption request
    const redemption = new GiftRedemption({
      userId,
      productId,
      productName,
      productImage,
      pointsRequired,
      userYearlyPointsAtRedemption,
      userMonthlyPointsAtRedemption,
      metadata: {
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip,
        deviceInfo: req.get('X-Device-Info') || 'Unknown'
      }
    });

    await redemption.save();

    // Create user notification
    const userNotification = new Notification({
      userId,
      title: 'Gift Redemption Request Submitted',
      message: `Your redemption request for ${productName} (${pointsRequired} points) has been submitted and is pending approval.`,
      type: 'info',
      priority: 'medium',
      data: {
        redemptionId: redemption._id,
        productName,
        pointsRequired,
        type: 'gift_redemption_submitted'
      },
      actionUrl: '/gifts'
    });

    await userNotification.save();

    // Update redemption with notification ID
    redemption.userNotificationId = userNotification._id;
    redemption.userNotificationSent = true;
    await redemption.save();

    // Create dashboard notification for admin
    const dashboardNotification = new Notification({
      userId: null, // System notification for dashboard
      title: 'New Gift Redemption Request',
      message: `${user.fullName} (${user.dealerCode}) requested to redeem ${productName} for ${pointsRequired} points.`,
      type: 'system',
      priority: 'high',
      data: {
        redemptionId: redemption._id,
        userId,
        userName: user.fullName,
        userDealerCode: user.dealerCode,
        productName,
        pointsRequired,
        userYearlyPoints: user.yearlyPoints,
        userMonthlyPoints: user.monthlyPoints,
        type: 'gift_redemption_request'
      },
      actionUrl: `/dashboard/redemptions/${redemption._id}`
    });

    await dashboardNotification.save();

    // Update redemption with dashboard notification ID
    redemption.dashboardNotificationId = dashboardNotification._id;
    redemption.dashboardNotificationSent = true;
    await redemption.save();

    res.status(201).json({
      success: true,
      message: 'Gift redemption request submitted successfully',
      data: {
        redemptionId: redemption._id,
        status: redemption.status,
        productName: redemption.productName,
        pointsRequired: redemption.pointsRequired,
        redemptionDate: redemption.redemptionDate
      }
    });

  } catch (error) {
    console.error('Gift redemption error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process gift redemption request',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// @desc    Get user's redemption history
// @route   GET api/gifts/my-redemptions
// @access  Private
export const getUserRedemptions = async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10, status } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      status: status || null
    };

    const redemptions = await GiftRedemption.getUserRedemptions(userId, options);
    
    // Get total count
    const query = { userId };
    if (status) query.status = status;
    const totalCount = await GiftRedemption.countDocuments(query);

    res.json({
      success: true,
      data: redemptions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get user redemptions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch redemption history'
    });
  }
};

// @desc    Get pending redemptions for dashboard
// @route   GET api/gifts/pending
// @access  Private (Admin only)
export const getPendingRedemptions = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit)
    };

    const redemptions = await GiftRedemption.getPendingRedemptions(options);
    
    const totalCount = await GiftRedemption.countDocuments({ status: 'pending' });

    res.json({
      success: true,
      data: redemptions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: Math.ceil(totalCount / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get pending redemptions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch pending redemptions'
    });
  }
};

// @desc    Process redemption (approve/deny)
// @route   PUT api/gifts/process/:id
// @access  Private (Admin only)
export const processRedemption = async (req, res) => {
  try {
    const { id } = req.params;
    const { action, adminNotes = '' } = req.body;
    const adminId = req.user.id;

    if (!['approve', 'deny'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action. Must be "approve" or "deny"'
      });
    }

    const redemption = await GiftRedemption.findById(id).populate('userId', 'fullName phoneNumber yearlyPoints');
    if (!redemption) {
      return res.status(404).json({
        success: false,
        message: 'Redemption request not found'
      });
    }

    if (redemption.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Redemption request has already been processed'
      });
    }

    let updatedUser = null;
    let notificationMessage = '';
    let notificationType = 'info';

    if (action === 'approve') {
      // Approve redemption and deduct points using PointsService
      await redemption.approve(adminId, adminNotes);

      try {
        // Use PointsService to deduct points for gift redemption
        const pointsResult = await PointsService.deductPointsForGift(
          redemption.userId._id,
          redemption.pointsRequired,
          {
            giftName: redemption.productName,
            adminId: adminId,
            redemptionId: redemption._id
          }
        );

        updatedUser = pointsResult.user;

        notificationMessage = `Congratulations! Your redemption request for ${redemption.productName} has been approved. ${redemption.pointsRequired} points have been deducted (${pointsResult.deductionDetails.deductedFromMonthly} from monthly, ${pointsResult.deductionDetails.deductedFromYearly} from yearly points).`;
        notificationType = 'success';
      } catch (pointsError) {
        // If points deduction fails, revert the redemption approval
        await redemption.deny(adminId, `Points deduction failed: ${pointsError.message}`);

        return res.status(400).json({
          success: false,
          message: `Failed to process redemption: ${pointsError.message}`
        });
      }
    } else {
      // Deny redemption
      await redemption.deny(adminId, adminNotes);
      notificationMessage = `Your redemption request for ${redemption.productName} has been declined. ${adminNotes ? `Reason: ${adminNotes}` : ''}`;
      notificationType = 'error';
    }

    // Send notification to user
    const userNotification = new Notification({
      userId: redemption.userId._id,
      title: `Gift Redemption ${action === 'approve' ? 'Approved' : 'Declined'}`,
      message: notificationMessage,
      type: notificationType,
      priority: 'high',
      data: {
        redemptionId: redemption._id,
        productName: redemption.productName,
        pointsRequired: redemption.pointsRequired,
        action,
        adminNotes,
        type: `gift_redemption_${action}d`
      },
      actionUrl: '/gifts'
    });

    await userNotification.save();

    res.json({
      success: true,
      message: `Redemption request ${action}d successfully`,
      data: {
        redemptionId: redemption._id,
        status: redemption.status,
        processedDate: redemption.processedDate,
        pointsDeducted: redemption.pointsDeducted,
        userPointsRemaining: updatedUser?.yearlyPoints || redemption.userId.yearlyPoints
      }
    });

  } catch (error) {
    console.error('Process redemption error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process redemption request'
    });
  }
};
