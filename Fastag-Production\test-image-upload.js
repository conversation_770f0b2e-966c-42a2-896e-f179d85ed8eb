#!/usr/bin/env node

/**
 * Test script to verify image upload functionality
 * This script simulates the mobile app's image upload process
 */

import fs from 'fs';
import path from 'path';
import FormData from 'form-data';
import fetch from 'node-fetch';

const BACKEND_URL = 'http://localhost:5000'; // Adjust as needed

// Create a test image file
const createTestImage = () => {
  const testImagePath = path.join(process.cwd(), 'test-profile.jpg');
  
  // Create a minimal JPEG file (1x1 pixel)
  const jpegHeader = Buffer.from([
    0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
    0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
    0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
    0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
    0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
    0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
    0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
    0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
    0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
    0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
    0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x00, 0xFF, 0xD9
  ]);
  
  fs.writeFileSync(testImagePath, jpegHeader);
  return testImagePath;
};

const testImageUpload = async () => {
  console.log('🧪 Testing Image Upload Process...\n');
  
  try {
    // Create test image
    const testImagePath = createTestImage();
    console.log('✅ Test image created:', testImagePath);
    console.log('📏 Test image size:', fs.statSync(testImagePath).size, 'bytes\n');
    
    // Create FormData (similar to mobile app)
    const formData = new FormData();
    
    // Add text fields
    formData.append('fullName', 'Test User');
    formData.append('password', 'testpassword123');
    formData.append('phoneNumber', '+919999999999');
    formData.append('dateOfBirth', '1990-01-01');
    formData.append('age', '34');
    formData.append('adharNumber', '123456789012');
    formData.append('panCardNumber', '**********');
    formData.append('pinCode', '123456');
    formData.append('state', 'Test State');
    formData.append('city', 'Test City');
    formData.append('address', 'Test Address');
    formData.append('dealerCode', 'TEST123');
    formData.append('role', 'customer');
    
    // Add profile photo
    formData.append('profilePhoto', fs.createReadStream(testImagePath), {
      filename: 'profile.jpg',
      contentType: 'image/jpeg'
    });
    
    console.log('📤 Sending registration request...');
    
    // Send request to backend
    const response = await fetch(`${BACKEND_URL}/api/auth/register`, {
      method: 'POST',
      body: formData,
      // Don't set Content-Type header - let FormData set it with boundary
    });
    
    const result = await response.json();
    
    console.log('📊 Response Status:', response.status);
    console.log('📋 Response Body:', JSON.stringify(result, null, 2));
    
    // Clean up test file
    fs.unlinkSync(testImagePath);
    console.log('🧹 Test image cleaned up');
    
    if (result.success) {
      console.log('\n✅ SUCCESS: Image upload is working correctly!');
    } else {
      console.log('\n❌ FAILED: Image upload has issues');
      console.log('🔍 Error details:');
      console.log('  - Message:', result.message);
      console.log('  - Field:', result.field || 'Not specified');
    }
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
    console.error('Stack:', error.stack);
  }
};

// Run the test
testImageUpload();
