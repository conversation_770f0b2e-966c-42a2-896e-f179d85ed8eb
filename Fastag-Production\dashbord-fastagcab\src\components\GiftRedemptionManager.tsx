import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Clock, User, Gift } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import getServerBaseUrl from '@/envConfig';

interface GiftRedemption {
  _id: string;
  userId: {
    _id: string;
    fullName: string;
    phoneNumber: string;
    dealerCode: string;
  };
  productName: string;
  productImage: string;
  pointsRequired: number;
  userYearlyPointsAtRedemption: number;
  status: 'pending' | 'approved' | 'denied';
  redemptionDate: string;
  processedDate?: string;
  adminNotes?: string;
}

const GiftRedemptionManager: React.FC = () => {
  const { user } = useAuth();
  const [redemptions, setRedemptions] = useState<GiftRedemption[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);
  const [adminNotes, setAdminNotes] = useState<{ [key: string]: string }>({});
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
const BackendURL = getServerBaseUrl();
  const API_BASE_URL = BackendURL;

  useEffect(() => {
    fetchPendingRedemptions();
  }, []);

  const fetchPendingRedemptions = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`${API_BASE_URL}api/gifts/pending`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      if (result.success) {
        setRedemptions(result.data);
      } else {
        setMessage({ type: 'error', text: 'Failed to fetch redemptions' });
      }
    } catch (error) {
      console.error('Error fetching redemptions:', error);
      setMessage({ type: 'error', text: 'Network error occurred' });
    } finally {
      setLoading(false);
    }
  };

  const processRedemption = async (redemptionId: string, action: 'approve' | 'deny') => {
    setProcessing(redemptionId);
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch(`${API_BASE_URL}api/gifts/process/${redemptionId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          adminNotes: adminNotes[redemptionId] || ''
        })
      });

      const result = await response.json();
      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: `Redemption ${action}d successfully` 
        });
        
        // Remove processed redemption from list
        setRedemptions(prev => prev.filter(r => r._id !== redemptionId));
        
        // Clear admin notes for this redemption
        setAdminNotes(prev => {
          const updated = { ...prev };
          delete updated[redemptionId];
          return updated;
        });
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to process redemption' });
      }
    } catch (error) {
      console.error('Error processing redemption:', error);
      setMessage({ type: 'error', text: 'Network error occurred' });
    } finally {
      setProcessing(null);
    }
  };

  const handleNotesChange = (redemptionId: string, notes: string) => {
    setAdminNotes(prev => ({
      ...prev,
      [redemptionId]: notes
    }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <div className="text-center">
            <Clock className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>Loading redemption requests...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Gift Redemption Requests
          </CardTitle>
        </CardHeader>
        <CardContent>
          {message && (
            <Alert className={`mb-4 ${message.type === 'error' ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'}`}>
              <AlertDescription className={message.type === 'error' ? 'text-red-800' : 'text-green-800'}>
                {message.text}
              </AlertDescription>
            </Alert>
          )}

          {redemptions.length === 0 ? (
            <div className="text-center py-8">
              <Gift className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">No pending redemption requests</p>
            </div>
          ) : (
            <div className="space-y-4">
              {redemptions.map((redemption) => (
                <Card key={redemption._id} className="border-l-4 border-l-orange-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <img
                          src={redemption.productImage}
                          alt={redemption.productName}
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        <div>
                          <h3 className="font-semibold text-lg">{redemption.productName}</h3>
                          <p className="text-sm text-gray-600">
                            {redemption.pointsRequired} points required
                          </p>
                          <p className="text-xs text-gray-500">
                            Requested on {formatDate(redemption.redemptionDate)}
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
                        <Clock className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    </div>

                    <div className="bg-gray-50 rounded-lg p-3 mb-4">
                      <div className="flex items-center gap-2 mb-2">
                        <User className="h-4 w-4 text-gray-600" />
                        <span className="font-medium">User Details</span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Name:</span>
                          <span className="ml-2 font-medium">{redemption.userId.fullName}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Phone:</span>
                          <span className="ml-2 font-medium">{redemption.userId.phoneNumber}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Dealer Code:</span>
                          <span className="ml-2 font-medium">{redemption.userId.dealerCode}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Available Points:</span>
                          <span className="ml-2 font-medium">{redemption.userYearlyPointsAtRedemption}</span>
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium mb-2">Admin Notes (Optional)</label>
                      <Textarea
                        placeholder="Add any notes about this redemption decision..."
                        value={adminNotes[redemption._id] || ''}
                        onChange={(e) => handleNotesChange(redemption._id, e.target.value)}
                        className="min-h-[80px]"
                      />
                    </div>

                    <div className="flex gap-3">
                      <Button
                        onClick={() => processRedemption(redemption._id, 'approve')}
                        disabled={processing === redemption._id}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        {processing === redemption._id ? 'Processing...' : 'Approve'}
                      </Button>
                      <Button
                        onClick={() => processRedemption(redemption._id, 'deny')}
                        disabled={processing === redemption._id}
                        variant="destructive"
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        {processing === redemption._id ? 'Processing...' : 'Deny'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default GiftRedemptionManager;
