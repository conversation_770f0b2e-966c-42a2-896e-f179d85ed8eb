import React, { useState, useEffect } from 'react'
import { X, Save, User, Phone, MapPin, Calendar, CreditCard, Building, AlertCircle } from 'lucide-react'
import Button from '../ui/Button'
import { usersAPI } from '../../lib/api'
import toast from 'react-hot-toast'

interface User {
  _id: string
  fullName: string
  phoneNumber: string
  dateOfBirth: string
  age: number
  pinCode: string
  state: string
  city: string
  address: string
  adharNumber?: string
  panCardNumber?: string
  dealerCode: string
  status: 'pending' | 'approved' | 'denied'
  role: string
  profilePhoto?: string
  adharCard?: string
  panCard?: string
  bankDetails?: string
}

interface EditUserModalProps {
  user: User | null
  isOpen: boolean
  onClose: () => void
  onUserUpdated: () => void
}

const EditUserModal: React.FC<EditUserModalProps> = ({ user, isOpen, onClose, onUserUpdated }) => {
  const [formData, setFormData] = useState<Partial<User>>({})
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (user) {
      setFormData({
        fullName: user.fullName,
        phoneNumber: user.phoneNumber,
        dateOfBirth: user.dateOfBirth ? user.dateOfBirth.split('T')[0] : '',
        age: user.age,
        pinCode: user.pinCode,
        state: user.state,
        city: user.city,
        address: user.address,
        adharNumber: user.adharNumber || '',
        panCardNumber: user.panCardNumber || '',
        dealerCode: user.dealerCode,
        status: user.status,
        role: user.role
      })
    }
  }, [user])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target

    // Clear error for this field when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }

    // Auto-uppercase PAN card number
    const processedValue = name === 'panCardNumber' ? value.toUpperCase() : value

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }))
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // Full Name validation - letters and spaces only, 2-100 characters
    if (formData.fullName) {
      if (formData.fullName.length < 2 || formData.fullName.length > 100) {
        newErrors.fullName = 'Full name must be between 2 and 100 characters'
      } else if (!/^[a-zA-Z\s]+$/.test(formData.fullName)) {
        newErrors.fullName = 'Full name can only contain letters and spaces'
      }
    }

    // Phone Number validation - Indian phone number format
    if (formData.phoneNumber && !/^[6-9]\d{9}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please provide a valid 10-digit Indian phone number'
    }

    // Age validation
    if (formData.age && (formData.age < 18 || formData.age > 100)) {
      newErrors.age = 'Age must be between 18 and 100'
    }

    // Aadhar Number validation - 12 digits
    if (formData.adharNumber && !/^\d{12}$/.test(formData.adharNumber)) {
      newErrors.adharNumber = 'Aadhar number must be 12 digits'
    }

    // PAN Card validation - format **********
    if (formData.panCardNumber && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(formData.panCardNumber)) {
      newErrors.panCardNumber = 'Please provide a valid PAN card number'
    }

    // PIN Code validation - 6 digits
    if (formData.pinCode && !/^\d{6}$/.test(formData.pinCode)) {
      newErrors.pinCode = 'Pin code must be 6 digits'
    }

    // State validation - 2-50 characters
    if (formData.state && (formData.state.length < 2 || formData.state.length > 50)) {
      newErrors.state = 'State must be between 2 and 50 characters'
    }

    // City validation - 2-50 characters
    if (formData.city && (formData.city.length < 2 || formData.city.length > 50)) {
      newErrors.city = 'City must be between 2 and 50 characters'
    }

    // Address validation - 10-500 characters
    if (formData.address && (formData.address.length < 10 || formData.address.length > 500)) {
      newErrors.address = 'Address must be between 10 and 500 characters'
    }

    // Dealer Code validation - 2-20 characters
    if (formData.dealerCode && (formData.dealerCode.length < 2 || formData.dealerCode.length > 20)) {
      newErrors.dealerCode = 'Dealer code must be between 2 and 20 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    // Calculate age if date of birth changed
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth)
      const today = new Date()
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }

      formData.age = age
    }

    // Validate form before submission
    if (!validateForm()) {
      toast.error('Please fix the validation errors before submitting')
      return
    }

    try {
      setLoading(true)

      const response = await usersAPI.updateUser(user._id, formData)

      if (response.success) {
        toast.success('User updated successfully')
        onUserUpdated()
        onClose()
        setErrors({}) // Clear errors on success
      } else {
        toast.error(response.message || 'Failed to update user')

        // Handle backend validation errors
        if (response.errors && Array.isArray(response.errors)) {
          const backendErrors: Record<string, string> = {}
          response.errors.forEach((error: any) => {
            if (error.path) {
              backendErrors[error.path] = error.msg
            }
          })
          setErrors(backendErrors)
        }
      }
    } catch (error: any) {
      console.error('Update user error:', error)

      // Handle specific error responses
      if (error.response?.data?.message) {
        toast.error(error.response.data.message)

        // Handle duplicate field errors
        if (error.response.data.field && error.response.data.error === 'DUPLICATE_KEY') {
          setErrors({
            [error.response.data.field]: error.response.data.message
          })
        }
      } else {
        toast.error('Failed to update user')
      }
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen || !user) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <User className="h-6 w-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">Edit User Details</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium" style={{ color: '#1A1A1A' }}>
                <User className="h-5 w-5 mr-2" style={{ color: '#1ca63a' }} />
                Personal Information
              </h3>

              <div>
                <label className="label" style={{ color: '#1A1A1A' }}>Full Name</label>
                <input
                  type="text"
                  name="fullName"
                  value={formData.fullName || ''}
                  onChange={handleInputChange}
                  className={`input ${errors.fullName ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-primary-500'}`}
                  style={{
                    borderColor: errors.fullName ? '#df5921' : '#7e8689',
                    color: '#1A1A1A'
                  }}
                  required
                />
                {errors.fullName && (
                  <div className="flex items-center mt-1 text-sm" style={{ color: '#df5921' }}>
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errors.fullName}
                  </div>
                )}
              </div>

              <div>
                <label className="label text-gray-700">Phone Number</label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber || ''}
                    onChange={handleInputChange}
                    className={`input pl-10 ${errors.phoneNumber ? 'border-red-500 focus:border-red-500' : ''}`}
                    required
                  />
                </div>
                {errors.phoneNumber && (
                  <div className="flex items-center mt-1 text-sm text-red-600">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errors.phoneNumber}
                  </div>
                )}
              </div>

              <div>
                <label className="label text-gray-700">Date of Birth</label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="date"
                    name="dateOfBirth"
                    value={formData.dateOfBirth || ''}
                    onChange={handleInputChange}
                    className="input pl-10"
                  />
                </div>
              </div>

              <div>
                <label className="label text-gray-700">Age</label>
                <input
                  type="number"
                  name="age"
                  value={formData.age || ''}
                  onChange={handleInputChange}
                  className="input"
                  min="18"
                  max="100"
                />
              </div>

              <div>
                <label className="label text-gray-700">Role</label>
                <select
                  name="role"
                  value={formData.role || ''}
                  onChange={handleInputChange}
                  className="input"
                >
                  <option value="electrician">Electrician</option>
                  <option value="distributor">Distributor</option>
                  <option value="admin">Admin</option>
                </select>
              </div>

              <div>
                <label className="label text-gray-700">Status</label>
                <select
                  name="status"
                  value={formData.status || ''}
                  onChange={handleInputChange}
                  className="input"
                  required
                >
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="denied">Denied</option>
                </select>
              </div>
            </div>

            {/* Address & Documents */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <MapPin className="h-5 w-5 mr-2 text-blue-600" />
                Address & Documents
              </h3>

              <div>
                <label className="label text-gray-700">Address</label>
                <textarea
                  name="address"
                  value={formData.address || ''}
                  onChange={handleInputChange}
                  className="input"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="label text-gray-700">City</label>
                  <input
                    type="text"
                    name="city"
                    value={formData.city || ''}
                    onChange={handleInputChange}
                    className="input"
                    required
                  />
                </div>
                <div>
                  <label className="label text-gray-700">State</label>
                  <input
                    type="text"
                    name="state"
                    value={formData.state || ''}
                    onChange={handleInputChange}
                    className="input"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="label text-gray-700">PIN Code</label>
                <input
                  type="text"
                  name="pinCode"
                  value={formData.pinCode || ''}
                  onChange={handleInputChange}
                  className="input"
                  pattern="[0-9]{6}"
                  required
                />
              </div>

              <div>
                <label className="label text-gray-700">Aadhar Number</label>
                <div className="relative">
                  <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    name="adharNumber"
                    value={formData.adharNumber || ''}
                    onChange={handleInputChange}
                    className="input pl-10"
                    pattern="[0-9]{12}"
                  />
                </div>
              </div>

              <div>
                <label className="label text-gray-700">PAN Card Number</label>
                <div className="relative">
                  <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    name="panCardNumber"
                    value={formData.panCardNumber || ''}
                    onChange={handleInputChange}
                    className="input pl-10"
                    pattern="[A-Z]{5}[0-9]{4}[A-Z]{1}"
                  />
                </div>
              </div>

              <div>
                <label className="label text-gray-700">Dealer Code</label>
                <div className="relative">
                  <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    name="dealerCode"
                    value={formData.dealerCode || ''}
                    onChange={handleInputChange}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 mt-8 pt-6 border-t">
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={loading}
              className="flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update User
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default EditUserModal
