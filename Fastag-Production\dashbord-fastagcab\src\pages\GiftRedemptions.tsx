// AdminRechargeDashboard.tsx

import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card';
import Button from '@/components/ui/Button';
import LoadingSpinner from '../components/ui/LoadingSpinner';
import { Badge } from '../components/ui/badge';
import { Textarea } from '../components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';
import toast from 'react-hot-toast';
import {
  Clock,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  Phone,
  RefreshCw
} from 'lucide-react';
import getServerBaseUrl from '@/envConfig';

interface User {
  _id: string;
  fullName: string;
  phoneNumber: string;
}

interface Recharge {
  _id: string;
  userId: User;
  mobileNumber: string;
  operator: string;
  rechargeAmount: number;
  pointsDeducted: number;
  status: 'pending' | 'completed' | 'denied';
  rechargeDate: string;
  adminNotes?: string;
}

const AdminRechargeDashboard: React.FC = () => {
  const [recharges, setRecharges] = useState<Recharge[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);
  const [selectedRecharge, setSelectedRecharge] = useState<Recharge | null>(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'completed' | 'denied' | null>(null);

  const backendUrl = getServerBaseUrl();

  const fetchRecharges = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('authToken');
      const res = await fetch(`${backendUrl}api/recharges/admin`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      const result = await res.json();
      if (result.success) {
        setRecharges(result.data.recharges);
      } else {
        toast.error(result.message || 'Failed to fetch recharges');
      }
    } catch (error: any) {
      toast.error('Network error. Please try again.');
      console.error('Fetch error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRecharges();
  }, []);

  const handleAction = (recharge: Recharge, type: 'completed' | 'denied') => {
    setSelectedRecharge(recharge);
    setActionType(type);
    setAdminNotes('');
    setDialogOpen(true);
  };

  const processRecharge = async () => {
    if (!selectedRecharge || !actionType) return;

    if (actionType === 'denied' && !adminNotes.trim()) {
      toast.error('Admin notes required for denial.');
      return;
    }

    try {
      setProcessing(selectedRecharge._id);
      const token = localStorage.getItem('authToken');
      const res = await fetch(`${backendUrl}api/recharges/admin/${selectedRecharge._id}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: actionType,
          adminNotes: adminNotes.trim(),
          processedBy: 'admin_user_id', // Replace with real admin ID
        }),
      });
      const result = await res.json();
      if (result.success) {
        toast.success(`Recharge ${actionType === 'completed' ? 'approved' : 'denied'} successfully`);
        setRecharges(prev => prev.filter(r => r._id !== selectedRecharge._id));
        setDialogOpen(false);
        setSelectedRecharge(null);
        setActionType(null);
        setAdminNotes('');
      } else {
        toast.error(result.message || 'Failed to process recharge');
      }
    } catch (error) {
      toast.error('Error processing recharge');
      console.error('Process error:', error);
    } finally {
      setProcessing(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'completed':
        return <Badge variant="outline" className="text-green-600 border-green-600"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>;
      case 'denied':
        return <Badge variant="outline" className="text-red-600 border-red-600"><XCircle className="w-3 h-3 mr-1" />Denied</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const formatDate = (date: string) =>
    new Date(date).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start sm:items-center flex-col sm:flex-row gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Recharge Requests</h1>
          <p className="text-gray-600 mt-1">Manage and approve/deny recharge requests</p>
        </div>
        <Button
          onClick={fetchRecharges}
          variant="outline"
          className="flex items-center gap-2"
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64 flex-col gap-4">
          <LoadingSpinner size="lg" />
          <p className="text-gray-500">Loading recharge requests...</p>
        </div>
      ) : recharges.length === 0 ? (
        <Card>
          <CardContent className="h-64 flex flex-col justify-center items-center text-gray-500">
            <Phone className="w-10 h-10 mb-2" />
            No recharge requests found
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {recharges.map((item) => (
            <Card key={item._id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{item.userId?.fullName || 'Unknown User'}</CardTitle>
                    <p className="text-sm text-gray-600">{item.mobileNumber} • {item.operator}</p>
                    <p className="text-sm text-gray-500">{formatDate(item.rechargeDate)}</p>
                  </div>
                  {getStatusBadge(item.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-sm">
                  <p><strong>Amount:</strong> ₹{item.rechargeAmount}</p>
                  <p><strong>Points Deducted:</strong> {item.pointsDeducted}</p>
                </div>
                {item.status === 'pending' && (
                  <div className="flex flex-wrap gap-3">
                    <Button
                      className="bg-green-600 hover:bg-green-700"
                      onClick={() => handleAction(item, 'completed')}
                      disabled={processing === item._id}
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Approve
                    </Button>
                    <Button
                      variant="danger"
                      onClick={() => handleAction(item, 'denied')}
                      disabled={processing === item._id}
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Deny
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Dialog for approve/deny */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{actionType === 'completed' ? 'Approve Recharge' : 'Deny Recharge'}</DialogTitle>
          </DialogHeader>

          {selectedRecharge && (
            <div className="space-y-4">
              <div className="bg-gray-50 p-3 rounded">
                <p><strong>User:</strong> {selectedRecharge.userId.fullName}</p>
                <p><strong>Phone:</strong> {selectedRecharge.mobileNumber}</p>
                <p><strong>Amount:</strong> ₹{selectedRecharge.rechargeAmount}</p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Admin Notes {actionType === 'denied' && '(Required)'}
                </label>
                <Textarea
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="Add admin notes..."
                  rows={3}
                />
              </div>

              <div className="flex space-x-3 pt-2">
                <Button
                  onClick={processRecharge}
                  disabled={
                    processing === selectedRecharge._id ||
                    (actionType === 'denied' && !adminNotes.trim())
                  }
                  className={actionType === 'completed' ? 'bg-green-600 hover:bg-green-700' : ''}
                  variant={actionType === 'denied' ? 'danger' : 'primary'}
                >
                  {processing === selectedRecharge._id ? 'Processing...' : `Confirm ${actionType}`}
                </Button>
                <Button variant="outline" onClick={() => setDialogOpen(false)}>Cancel</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminRechargeDashboard;
