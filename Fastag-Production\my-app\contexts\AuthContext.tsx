import React, {
  createContext,
  ReactNode,
  useContext,
  useState,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import InteraktWhatsAppService from "@/services/interaktWhatsAppService";
import TestOTPService from "@/services/testOTPService";
import getServerBaseUrl from "@/envConfig";
import socketService from "@/services/socketService";
const BackendURL = getServerBaseUrl();

interface User {
  _id: string;
  fullName: string;
  phoneNumber: string;
  role: string;
  address?: string;
  age?: number;
  dateOfBirth?: string;
  profilePhoto?: string;
  adharCard?: string;
  panCard?: string;
  bankDetails?: string;
  status?: "pending" | "approved" | "rejected";
  isVerified?: boolean;
  dealerCode?: string;
  monthlyPoints?: number;
  yearlyPoints?: number;
}

interface RegistrationData {
  fullName: string;
  password: string;
  phoneNumber: string;
  dateOfBirth: Date;
  age: number;
  adharNumber?: string;
  panCardNumber?: string;
  pinCode: string;
  state: string;
  city: string;
  address: string;
  dealerCode: string;
  role: "Electrician" | "Distributor";
  profilePhoto: string;
  adharCard?: string;
  panCard?: string;
  bankDetails?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (
    phoneNumber: string,
    password: string
  ) => Promise<{ success: boolean; message: string }>;
  register: (
    data: RegistrationData
  ) => Promise<{ success: boolean; message: string }>;
  sendOtpRequest: (
    phoneNumber: string
  ) => Promise<{ success: boolean; message: string }>;
  resendOtpRequest: (
    phoneNumber: string
  ) => Promise<{ success: boolean; message: string }>;
  verifyOtpAndRegister: (
    phoneNumber: string,
    otp: string,
    userData: any
  ) => Promise<{ success: boolean; message: string }>;
  forgotPasswordSendOTP: (
    phoneNumber: string
  ) => Promise<{ success: boolean; message: string }>;
  resetPasswordWithOTP: (
    phoneNumber: string,
    otp: string,
    newPassword: string
  ) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  isAuthenticated: boolean;
  // Additional functions to prevent crashes
  addPoints?: (
    points: number
  ) => Promise<{ success: boolean; message: string }>;
  processQRCode?: (
    data: string
  ) => Promise<{ success: boolean; message: string }>;
  processRecharge?: (
    amount: number
  ) => Promise<{ success: boolean; message: string }>;
  updateUserPoints?: (
    monthlyPoints: number,
    yearlyPoints?: number
  ) => Promise<{ success: boolean; message: string }>;
  updateUserProfile?: (
    profileData: any
  ) => Promise<{ success: boolean; message: string }>;
  refreshUserData?: () => Promise<{
    success: boolean;
    message: string;
    data?: any;
  }>;
  verifyOtpRequest: (
    phoneNumber: string,
    otp: string
  ) => Promise<{ success: boolean; message: string }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true); // Start with true to check stored auth

  // Use environment variable or fallback to local server for testing
  const backendUrl = BackendURL;

  // Debug: Log the backend URL being used
  console.log(
    "🔧 Environment EXPO_PUBLIC_API_URL:",
    process.env.EXPO_PUBLIC_API_URL
  );
  console.log("🔧 Final backendUrl being used:", backendUrl);
  // console.log('🔧 Production backend URL: https://fastag.bd1.pro/');

  // Check for stored authentication on app start
  useEffect(() => {
    checkStoredAuth();
  }, []);

  // Socket.IO integration for real-time points updates
  useEffect(() => {
    if (user) {
      console.log("🔌 User logged in, connecting to Socket.IO...");

      // Connect to socket
      socketService.connect();

      // Set up points update listener
      const unsubscribePointsUpdate = socketService.onPointsUpdate((data) => {
        console.log("📊 Received real-time points update:", data);

        // Update user state with new points
        setUser((prevUser) => {
          if (prevUser) {
            const updatedUser = {
              ...prevUser,
              monthlyPoints: data.monthlyPoints,
              yearlyPoints: data.yearlyPoints,
            };

            // Update stored user data
            AsyncStorage.setItem("user", JSON.stringify(updatedUser)).catch(
              (error) => {
                console.error("❌ Error updating stored user data:", error);
              }
            );

            return updatedUser;
          }
          return prevUser;
        });
      });

      // Set up connection status listener
      const unsubscribeConnection = socketService.onConnectionChange(
        (connected) => {
          console.log(
            `🔌 Socket connection status: ${
              connected ? "Connected" : "Disconnected"
            }`
          );
        }
      );

      // Re-authenticate socket when user changes
      socketService.reAuthenticate();

      // Cleanup function
      return () => {
        console.log("🔌 Cleaning up socket listeners...");
        unsubscribePointsUpdate();
        unsubscribeConnection();
      };
    } else {
      console.log("🔌 User logged out, disconnecting from Socket.IO...");
      socketService.disconnect();
    }
  }, [user?._id]); // Only re-run when user ID changes

  const checkStoredAuth = async () => {
    try {
      const storedToken = await AsyncStorage.getItem("authToken");
      const storedUser = await AsyncStorage.getItem("user");

      if (storedToken && storedUser) {
        // First, set the stored user data immediately to prevent blank screen
        const parsedStoredUser = JSON.parse(storedUser);
        setUser(parsedStoredUser);

        // Then verify token and fetch fresh data from backend
        try {
          console.log("🔄 Verifying token and fetching fresh user data...");
          const response = await fetch(`${backendUrl}api/auth/verify-token`, {
            method: "GET",
            headers: {
              Authorization: `Bearer ${storedToken}`,
              "Content-Type": "application/json",
            },
          });

          if (response.ok) {
            const data = await response.json();
            console.log(
              "✅ Token verification successful, received user data:",
              data
            );

            if (data.success && data.user) {
              // Update user state with fresh data from server (including updated points)
              setUser(data.user);
              // Update stored user data with fresh data from server
              await AsyncStorage.setItem("user", JSON.stringify(data.user));
              console.log("✅ User data updated with fresh points:", {
                monthlyPoints: data.user.monthlyPoints,
                yearlyPoints: data.user.yearlyPoints,
              });
            } else {
              console.log("❌ Token verification failed, clearing storage");
              // Token is invalid, clear storage
              await AsyncStorage.removeItem("authToken");
              await AsyncStorage.removeItem("user");
              setUser(null);
            }
          } else {
            console.log(
              "❌ Token verification request failed, clearing storage"
            );
            // Token is invalid, clear storage
            await AsyncStorage.removeItem("authToken");
            await AsyncStorage.removeItem("user");
            setUser(null);
          }
        } catch (error) {
          console.log(
            "⚠️ Token verification failed due to network error, using stored user data:",
            error
          );
          // If backend is not reachable, keep using stored user data (already set above)
          // This ensures the app works offline but may have stale points data
        }
      }
    } catch (error) {
      console.error("❌ Error checking stored auth:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Debug logging for backend URL
  console.log("🔧 AuthContext: Backend URL configured as:", backendUrl);
  console.log(
    "🔧 AuthContext: Environment variable EXPO_PUBLIC_API_URL:",
    process.env.EXPO_PUBLIC_API_URL
  );

  // Test network connectivity
  const testNetworkConnectivity = async () => {
    try {
      console.log("🔧 Testing network connectivity to:", backendUrl);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // Reduced timeout to 5 seconds

      const response = await fetch(`${backendUrl}/`, {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      console.log("🔧 Network test response status:", response.status);
      console.log("🔧 Network test response ok:", response.ok);
      return response.ok;
    } catch (error: any) {
      console.log("🔧 Network test failed:", error.message);
      console.log(
        "ℹ️  This is normal if the backend server is not running locally"
      );
      console.log("ℹ️  The app will still function with fallback services");
      return false;
    }
  };

  // Test connectivity on component mount
  React.useEffect(() => {
    testNetworkConnectivity();
  }, []);

  const login = async (phoneNumber: string, password: string) => {
    setIsLoading(true);

    // Test connectivity first
    console.log("🔧 Testing connectivity before login...");
    const isConnected = await testNetworkConnectivity();
    if (!isConnected) {
      console.log(
        "🚨 Network connectivity test failed, but proceeding with login attempt..."
      );
    }

    // Force the correct URL
    const loginUrl = `${backendUrl}api/auth/login`;

    try {
      console.log("🔄 [UPDATED] Attempting login to:", loginUrl);
      console.log("🔄 [UPDATED] Login payload:", {
        phoneNumber,
        password: "***",
      });
      console.log("🔄 [UPDATED] Backend URL:", backendUrl);
      console.log("🔄 [UPDATED] Full URL being used:", loginUrl);

      const res = await fetch(loginUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ phoneNumber, password }),
      });

      console.log("Response status:", res.status);
      console.log("Response headers:", res.headers);

      // Check content type to avoid JSON parsing errors
      const contentType = res.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        console.error("🚨 Server returned non-JSON response:", contentType);
        const text = await res.text();
        console.error("🚨 Response text:", text.substring(0, 200) + "...");
        return {
          success: false,
          message: "Server returned invalid response format",
        };
      }

      const data = await res.json();
      console.log("Response data:", data);

      if (res.ok && data.success) {
        const userData = data.data.user;
        const token = data.data.token;

        // Save token and user data to AsyncStorage
        await AsyncStorage.setItem("authToken", token);
        await AsyncStorage.setItem("user", JSON.stringify(userData));

        setUser(userData);
        console.log("Logged-in user:", userData);

        // Re-authenticate socket after successful login
        setTimeout(() => {
          socketService.reAuthenticate();
        }, 1000);

        return { success: true, message: "Login successful" };
      } else {
        console.error("Login failed:", data);
        return { success: false, message: data.message || "Login failed" };
      }
    } catch (error: any) {
      console.error("🚨 Login error:", error);
      console.error("🚨 Login error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });

      // Log the error details for debugging
      console.error("🚨 Login failed:", {
        name: error?.name,
        message: error?.message,
        stack: error?.stack,
      });

      // Check if it's a network error
      if (
        error.message.includes("Network request failed") ||
        error.message.includes("fetch") ||
        error.message.includes("JSON Parse error")
      ) {
        console.error(
          "❌ Network error during login - backend server may be unreachable"
        );
        return {
          success: false,
          message:
            "Cannot connect to server. Please check your internet connection and try again.",
        };
      }

      return { success: false, message: `Login failed: ${error.message}` };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegistrationData) => {
    setIsLoading(true);
    try {
      console.log("📝 frontend Register function called");

      // Create FormData for file uploads
      const formData = new FormData();

      // Add text fields
      formData.append("fullName", data.fullName);
      formData.append("password", data.password);
      formData.append("phoneNumber", data.phoneNumber);

      const dateOfBirth = data.dateOfBirth instanceof Date ? data.dateOfBirth : new Date(data.dateOfBirth);
      formData.append("dateOfBirth", dateOfBirth.toISOString().split("T")[0]);

      formData.append("age", data.age.toString());
      formData.append("pinCode", data.pinCode);
      formData.append("state", data.state);
      formData.append("city", data.city);
      formData.append("address", data.address);
      formData.append("dealerCode", data.dealerCode);
      formData.append("role", data.role);

      if (data.adharNumber) formData.append("adharNumber", data.adharNumber);
      if (data.panCardNumber) formData.append("panCardNumber", data.panCardNumber);

      // Handle file uploads with proper error checking
      const addFileToFormData = (fieldName: string, uri: string) => {
        if (!uri) return false;
        
        try {
          // Ensure proper URI format
          let fileUri = uri;
          if (!fileUri.startsWith("file://") && !fileUri.startsWith("content://") && !fileUri.startsWith("http")) {
            fileUri = `file://${fileUri}`;
          }

          const fileObject = {
            uri: fileUri,
            type: "image/jpeg",
            name: `${fieldName}.jpg`,
          };

          formData.append(fieldName, fileObject as any);
          console.log(`✅ ${fieldName} added to FormData`);
          return true;
        } catch (error) {
          console.error(`❌ Failed to add ${fieldName}:`, error);
          return false;
        }
      };

      // Add profile photo (required)
      if (!data.profilePhoto) {
        return {
          success: false,
          message: "Profile photo is required for registration",
        };
      }
      
      if (!addFileToFormData("profilePhoto", data.profilePhoto)) {
        return {
          success: false,
          message: "Failed to process profile photo",
        };
      }

      // Add optional files
      if (data.adharCard) addFileToFormData("adharCard", data.adharCard);
      if (data.panCard) addFileToFormData("panCard", data.panCard);
      if (data.bankDetails) addFileToFormData("bankDetails", data.bankDetails);

      const registerUrl = `${backendUrl}api/auth/register`;
      console.log("🌐 Registration URL:", registerUrl);

      const res = await fetch(registerUrl, {
        method: "POST",
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const resData = await res.json();
      console.log("📡 Registration response:", resData);

      if (res.status === 200 || res.status === 201) {
        return {
          success: true,
          message: resData.message || "Registration successful",
        };
      } else {
        return {
          success: false,
          message: resData.message || "Server error during registration",
        };
      }
    } catch (error: any) {
      console.error("🚨 Registration error:", error);
      return {
        success: false,
        message: `Registration failed: ${error.message}`,
      };
    } finally {
      setIsLoading(false);
    }
  };

  // OTP functions - Use WhatsApp service for registration
  const sendOtpRequest = async (phoneNumber: string) => {
    try {
      setIsLoading(true);
      console.log("🚀 AUTHCONTEXT: Sending OTP to:", phoneNumber);

      // Use Interakt WhatsApp service for registration OTP
      console.log("📱 Using Interakt WhatsApp service for registration...");
      const whatsappResult = await InteraktWhatsAppService.sendOTP(phoneNumber);

      if (whatsappResult.success) {
        console.log("✅ Registration OTP sent successfully via WhatsApp");
        return {
          success: true,
          message:
            "OTP sent successfully to your WhatsApp. Please check your messages.",
        };
      } else {
        console.error("❌ WhatsApp service failed:", whatsappResult.message);

        // Fallback to test service if WhatsApp fails
        console.log("🔄 WhatsApp failed, trying test service as fallback...");
        const testResult = await TestOTPService.sendOTP(phoneNumber);

        if (testResult.success) {
          console.log("✅ Test OTP service succeeded as fallback");
          return {
            success: true,
            message:
              "OTP sent successfully via test service. Check console for OTP.",
          };
        } else {
          console.error("❌ Both services failed");
          return {
            success: false,
            message: "Failed to send OTP. Please try again later.",
          };
        }
      }
    } catch (error) {
      console.error("❌ AUTHCONTEXT: Send OTP error:", error);
      return {
        success: false,
        message: "Network error while sending OTP",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtpAndRegister = async (
    phoneNumber: string,
    otp: string,
    userData: any
  ) => {
    try {
      setIsLoading(true);
      console.log(
        "🔍 AUTHCONTEXT: Verifying OTP for registration:",
        phoneNumber
      );

      // First try InteraktWhatsAppService verification (matches the send service)
      console.log("🔍 Using Interakt WhatsApp service for verification...");
      let verificationResult = await InteraktWhatsAppService.verifyOTP(
        phoneNumber,
        otp
      );

      // If Interakt verification fails, fallback to test service
      if (!verificationResult.success) {
        console.log(
          "🔄 Interakt verification failed, trying test service as fallback..."
        );
        verificationResult = await TestOTPService.verifyOTP(phoneNumber, otp);
      }

      if (verificationResult.success) {
        console.log(
          "✅ OTP verified successfully, proceeding with registration"
        );

        // If OTP is verified, proceed with user registration
        const registrationResult = await register(userData);
        console.log("REGISTRATION_RESULT:", registrationResult);

        if (registrationResult.success) {
          console.log("✅ Registration successful");
          return {
            success: true,
            message:
              "Registration completed successfully! Please login with your credentials.",
          };
        } else {
          console.error("❌ Registration failed:", registrationResult.message);
          return {
            success: false,
            message:
              registrationResult.message ||
              "Registration failed. Please try again.",
          };
        }
      } else {
        console.error(
          "❌ OTP verification failed:",
          verificationResult.message
        );
        return {
          success: false,
          message:
            verificationResult.message || "Invalid OTP. Please try again.",
        };
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      return {
        success: false,
        message: "Verification failed. Please try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Resend OTP function
  const resendOtpRequest = async (phoneNumber: string) => {
    try {
      setIsLoading(true);
      console.log("🔄 AUTHCONTEXT: Resending OTP to:", phoneNumber);

      // Use Interakt WhatsApp service for resend (matches the send service)
      console.log("🔄 Using Interakt WhatsApp service for resend...");
      const whatsappResult = await InteraktWhatsAppService.resendOTP(
        phoneNumber
      );

      if (whatsappResult.success) {
        console.log("✅ OTP resent successfully via WhatsApp");
        return {
          success: true,
          message: "OTP resent successfully to your WhatsApp.",
        };
      } else {
        console.log(
          "🔄 WhatsApp resend failed, trying test service as fallback..."
        );
        const testResult = await TestOTPService.resendOTP(phoneNumber);

        if (testResult.success) {
          console.log("✅ Test OTP service succeeded as fallback");
          return {
            success: true,
            message: "OTP resent successfully. Check console for OTP.",
          };
        } else {
          console.error("❌ Both resend services failed");
          return {
            success: false,
            message: "Failed to resend OTP. Please try again.",
          };
        }
      }
    } catch (error) {
      console.error("OTP resend error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem("authToken");
      await AsyncStorage.removeItem("user");
    } catch (error) {
      console.error("Error clearing auth data:", error);
    }

    // Disconnect socket on logout
    socketService.disconnect();

    setUser(null);
  };

  // Placeholder functions to prevent crashes - implement as needed
  const addPoints = async (points: number) => {
    console.log("Add points function called with:", points);
    return {
      success: true,
      message: `Added ${points} points successfully!`,
    };
  };

  const processQRCode = async (data: string) => {
    try {
      // Get the stored token
      const storedToken = await AsyncStorage.getItem("authToken");

      if (!storedToken) {
        return {
          success: false,
          message: "Authentication required. Please login again.",
        };
      }

      const response = await fetch(`${backendUrl}api/qr/process`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedToken}`,
        },
        body: JSON.stringify({ qrData: data }),
      });

      const result = await response.json();

      if (result.success) {
        // Update user points in context
        setUser((prev) =>
          prev
            ? {
                ...prev,
                monthlyPoints: result.totalMonthlyPoints,
                yearlyPoints: result.totalYearlyPoints,
              }
            : null
        );
      }

      return result;
    } catch (error) {
      console.error("QR processing error:", error);
      return {
        success: false,
        message: "Failed to process QR code",
      };
    }
  };

  const processRecharge = async (amount: number) => {
    console.log("Process recharge function called with:", amount);
    return {
      success: true,
      message: `Recharge of ₹${amount} processed successfully!`,
    };
  };

  const refreshUserData = async () => {
    try {
      const storedToken = await AsyncStorage.getItem("authToken");

      if (!storedToken) {
        return {
          success: false,
          message: "Authentication required. Please login again.",
        };
      }

      console.log("🔄 Refreshing user data from server...");
      const response = await fetch(`${backendUrl}api/auth/verify-token`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${storedToken}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();

        if (data.success && data.user) {
          // Update user state with fresh data from server
          setUser(data.user);
          // Update stored user data
          await AsyncStorage.setItem("user", JSON.stringify(data.user));

          console.log("✅ User data refreshed successfully:", {
            monthlyPoints: data.user.monthlyPoints,
            yearlyPoints: data.user.yearlyPoints,
          });

          return {
            success: true,
            message: "User data refreshed successfully",
            data: data.user,
          };
        }
      }

      return {
        success: false,
        message: "Failed to refresh user data",
      };
    } catch (error) {
      console.error("❌ Refresh user data error:", error);
      return {
        success: false,
        message: "Network error while refreshing user data",
      };
    }
  };

  const updateUserPoints = async (
    monthlyPoints: number,
    yearlyPoints?: number
  ) => {
    try {
      // Get the stored token
      const storedToken = await AsyncStorage.getItem("authToken");

      if (!storedToken || !user) {
        return {
          success: false,
          message: "Authentication required. Please login again.",
        };
      }

      const updateData: any = { monthlyPoints };
      if (yearlyPoints !== undefined) {
        updateData.yearlyPoints = yearlyPoints;
      }

      const response = await fetch(
        `${backendUrl}api/users/${user._id}/points`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${storedToken}`,
          },
          body: JSON.stringify(updateData),
        }
      );

      const result = await response.json();

      if (result.success) {
        // Update user points in context
        setUser((prev) =>
          prev
            ? {
                ...prev,
                monthlyPoints: result.data.monthlyPoints,
                yearlyPoints: result.data.yearlyPoints,
              }
            : null
        );

        // Update stored user data
        if (user) {
          const updatedUser = {
            ...user,
            monthlyPoints: result.data.monthlyPoints,
            yearlyPoints: result.data.yearlyPoints,
          };
          await AsyncStorage.setItem("user", JSON.stringify(updatedUser));
        }
      }

      return result;
    } catch (error) {
      console.error("Update user points error:", error);
      return {
        success: false,
        message: "Failed to update user points",
      };
    }
  };

  const updateUserProfile = async (profileData: any) => {
    try {
      const storedToken = await AsyncStorage.getItem("authToken");

      if (!storedToken || !user) {
        return {
          success: false,
          message: "Authentication required. Please login again.",
        };
      }

      // Create FormData for file uploads
      const formData = new FormData();

      // Add text fields
      if (profileData.name) formData.append("fullName", profileData.name);
      if (profileData.password)
        formData.append("password", profileData.password);
      if (profileData.phoneNumber)
        formData.append("phoneNumber", profileData.phoneNumber);
      if (profileData.dateOfBirth) {
        const dateOfBirth =
          profileData.dateOfBirth instanceof Date
            ? profileData.dateOfBirth
            : new Date(profileData.dateOfBirth);
        formData.append("dateOfBirth", dateOfBirth.toISOString().split("T")[0]);
      }
      if (profileData.age) formData.append("age", profileData.age.toString());
      if (profileData.adharNumber)
        formData.append("adharNumber", profileData.adharNumber);
      if (profileData.panCardNumber)
        formData.append("panCardNumber", profileData.panCardNumber);
      if (profileData.pinCode) formData.append("pinCode", profileData.pinCode);
      if (profileData.state) formData.append("state", profileData.state);
      if (profileData.city) formData.append("city", profileData.city);
      if (profileData.address) formData.append("address", profileData.address);
      if (profileData.dealerCode)
        formData.append("dealerCode", profileData.dealerCode);

      // Add files if they are new uploads (check if they are local URIs)
      if (
        profileData.profilePhoto &&
        (profileData.profilePhoto.startsWith("file://") ||
          profileData.profilePhoto.startsWith("content://"))
      ) {
        console.log(
          "📸 Adding new profile photo to FormData:",
          profileData.profilePhoto
        );

        const profilePhotoFile = {
          uri: profileData.profilePhoto,
          type: "image/jpeg",
          name: "profile.jpg",
        };

        formData.append("profilePhoto", profilePhotoFile as any);
        console.log("✅ Profile photo added to FormData");
      }

      if (
        profileData.adharCard &&
        (profileData.adharCard.startsWith("file://") ||
          profileData.adharCard.startsWith("content://"))
      ) {
        formData.append("adharCard", {
          uri: profileData.adharCard,
          type: "image/jpeg",
          name: "adhar.jpg",
        } as any);
      }

      if (
        profileData.panCard &&
        (profileData.panCard.startsWith("file://") ||
          profileData.panCard.startsWith("content://"))
      ) {
        formData.append("panCard", {
          uri: profileData.panCard,
          type: "image/jpeg",
          name: "pan.jpg",
        } as any);
      }

      if (
        profileData.bankDetails &&
        (profileData.bankDetails.startsWith("file://") ||
          profileData.bankDetails.startsWith("content://"))
      ) {
        formData.append("bankDetails", {
          uri: profileData.bankDetails,
          type: "image/jpeg",
          name: "bank.jpg",
        } as any);
      }

      const response = await fetch(`${backendUrl}api/users/${user._id}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${storedToken}`,
        },
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        // Update user in context
        setUser(result.data.user);
        // Update stored user data
        await AsyncStorage.setItem("user", JSON.stringify(result.data.user));
      }

      return result;
    } catch (error) {
      console.error("Update profile error:", error);
      return {
        success: false,
        message: "Failed to update profile",
      };
    }
  };

  // Forgot password functions - using WhatsApp via Interakt
  const forgotPasswordSendOTP = async (phoneNumber: string) => {
    try {
      setIsLoading(true);
      console.log(
        "🚀 AUTHCONTEXT: Sending forgot password OTP to WhatsApp:",
        phoneNumber
      );

      // Use Interakt WhatsApp service for forgot password OTP
      console.log(
        "🚀 AUTHCONTEXT: Using Interakt WhatsApp service for forgot password OTP..."
      );
      const resultWhatsApp = await InteraktWhatsAppService.sendOTP(phoneNumber);

      if (resultWhatsApp.success) {
        console.log("✅ Forgot password OTP sent successfully via WhatsApp");
        console.log(
          "🔢 Generated OTP (for debugging):",
          resultWhatsApp.data?.otp
        );
        return {
          success: true,
          message:
            "Password reset code sent to your WhatsApp. Please check your messages.",
        };
      } else {
        console.error("❌ WhatsApp service failed:", resultWhatsApp.message);

        // Fallback to backend API if WhatsApp fails
        console.log("🔄 WhatsApp failed, trying backend API as fallback...");
        const response = await fetch(`${backendUrl}api/auth/forgot-password`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ phoneNumber }),
        });

        const result = await response.json();
        return result;
      }
    } catch (error) {
      console.error("Forgot password send OTP error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const resetPasswordWithOTP = async (
    phoneNumber: string,
    otp: string,
    newPassword: string
  ) => {
    try {
      setIsLoading(true);
      console.log("🔍 AUTHCONTEXT: Resetting password for:", phoneNumber);

      // Skip Interakt verification since it's failing, go directly to backend
      console.log("🔄 Using backend verification directly...");
      const response = await fetch(`${backendUrl}api/auth/reset-password`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          phoneNumber: phoneNumber.trim(),
          otp: otp.trim(),
          newPassword,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error("❌ Backend response error:", result);
        return {
          success: false,
          message: result.message || "Password reset failed",
        };
      }

      return result;
    } catch (error) {
      console.error("Reset password error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtpRequest = async (phoneNumber: string, otp: string) => {
    try {
      setIsLoading(true);
      console.log(
        "🔍 AUTHCONTEXT: Verifying OTP for forgot password:",
        phoneNumber
      );

      // Use the same verification service as registration
      console.log("🔍 Using Interakt WhatsApp service for verification...");
      let verificationResult = await InteraktWhatsAppService.verifyOTP(
        phoneNumber,
        otp
      );

      // If Interakt verification fails, fallback to test service
      if (!verificationResult.success) {
        console.log(
          "🔄 Interakt verification failed, trying test service as fallback..."
        );
        verificationResult = await TestOTPService.verifyOTP(phoneNumber, otp);
      }

      if (verificationResult.success) {
        console.log("✅ OTP verified successfully for forgot password");
        return {
          success: true,
          message: "OTP verified successfully",
        };
      } else {
        console.error(
          "❌ OTP verification failed:",
          verificationResult.message
        );
        return {
          success: false,
          message:
            verificationResult.message || "Invalid OTP. Please try again.",
        };
      }
    } catch (error: any) {
      console.error("🚨 OTP verification error:", error);
      return {
        success: false,
        message: `Verification failed: ${error.message}`,
      };
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    login,
    register,
    sendOtpRequest,
    resendOtpRequest,
    verifyOtpAndRegister,
    forgotPasswordSendOTP,
    resetPasswordWithOTP,
    logout,
    isAuthenticated: !!user,
    addPoints,
    processQRCode,
    processRecharge,
    updateUserPoints,
    updateUserProfile,
    refreshUserData,
    verifyOtpRequest,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined)
    throw new Error("useAuth must be used within an AuthProvider");
  return context;
};
