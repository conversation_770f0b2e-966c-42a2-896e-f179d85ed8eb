import mongoose from 'mongoose';

const offerSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Offer title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Offer description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  imageUrl: {
    type: String,
    required: [true, 'Offer image is required'],
    trim: true
  },
  offerType: {
    type: String,
    enum: ['percentage', 'fixed_amount', 'buy_one_get_one', 'free_shipping', 'other'],
    required: [true, 'Offer type is required']
  },
  discountValue: {
    type: Number,
    required: function() {
      return this.offerType === 'percentage' || this.offerType === 'fixed_amount';
    },
    min: [0, 'Discount value cannot be negative']
  },
  minimumPurchase: {
    type: Number,
    default: 0,
    min: [0, 'Minimum purchase cannot be negative']
  },
  maximumDiscount: {
    type: Number,
    default: null,
    min: [0, 'Maximum discount cannot be negative']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  displayOrder: {
    type: Number,
    default: 0
  },
  startDate: {
    type: Date,
    required: [true, 'Start date is required'],
    default: Date.now
  },
  endDate: {
    type: Date,
    required: [true, 'End date is required']
  },
  usageLimit: {
    type: Number,
    default: null,
    min: [1, 'Usage limit must be at least 1']
  },
  usageCount: {
    type: Number,
    default: 0,
    min: [0, 'Usage count cannot be negative']
  },
  clickCount: {
    type: Number,
    default: 0
  },
  viewCount: {
    type: Number,
    default: 0
  },
  termsAndConditions: {
    type: String,
    trim: true,
    maxlength: [2000, 'Terms and conditions cannot exceed 2000 characters']
  }
}, {
  timestamps: true
});

// Virtual for checking if offer is expired
offerSchema.virtual('isExpired').get(function() {
  return this.endDate && this.endDate < new Date();
});

// Virtual for checking if offer is available
offerSchema.virtual('isAvailable').get(function() {
  const now = new Date();
  const isWithinDateRange = this.startDate <= now && (!this.endDate || this.endDate >= now);
  const isWithinUsageLimit = !this.usageLimit || this.usageCount < this.usageLimit;
  return this.isActive && isWithinDateRange && isWithinUsageLimit;
});

// Index for better query performance
offerSchema.index({ isActive: 1, displayOrder: 1 });
offerSchema.index({ startDate: 1, endDate: 1 });
offerSchema.index({ offerType: 1 });

const Offer = mongoose.model('Offer', offerSchema);

export default Offer;
