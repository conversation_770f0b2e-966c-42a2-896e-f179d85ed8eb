import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";
import * as ImageManipulator from "expo-image-manipulator";

interface BasicPhotoUploadProps {
  title: string;
  subtitle?: string;
  required?: boolean;
  value: string;
  onPhotoSelected: (uri: string) => void;
  error?: string;
  cameraOnly?: boolean;
}

const compressImage = async (uri: string): Promise<string> => {
  try {
    console.log("🔄 Compressing image:", uri);

    const manipResult = await ImageManipulator.manipulateAsync(
      uri,
      [
        { resize: { width: 800 } }, // Resize to max width 800px
      ],
      {
        compress: 0.7, // 70% quality
        format: ImageManipulator.SaveFormat.JPEG,
      }
    );

    console.log("✅ Image compressed:", manipResult.uri);
    return manipResult.uri;
  } catch (error) {
    console.error("❌ Image compression failed:", error);
    return uri; // Return original if compression fails
  }
};

export default function BasicPhotoUpload({
  title,
  subtitle,
  required = false,
  value,
  onPhotoSelected,
  error,
  cameraOnly = false,
}: BasicPhotoUploadProps) {
  const [isLoading, setIsLoading] = useState(false);

  const selectPhoto = async () => {
    setIsLoading(true);
    try {
      console.log("📸 Starting photo selection...");

      if (cameraOnly) {
        const cameraPermission =
          await ImagePicker.requestCameraPermissionsAsync();
        if (!cameraPermission.granted) {
          Alert.alert(
            "Permission Required",
            "Camera permission is required to take photos."
          );
          setIsLoading(false);
          return;
        }

        const result = await ImagePicker.launchCameraAsync({
          mediaTypes: ["images"],
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
          allowsMultipleSelection: false,
        });

        if (!result.canceled && result.assets && result.assets.length > 0) {
          const uri = result.assets[0].uri;
          console.log("📸 Original photo URI:", uri);

          // Compress the image
          const compressedUri = await compressImage(uri);
          const formattedUri = compressedUri.startsWith("file://")
            ? compressedUri
            : `file://${compressedUri}`;

          onPhotoSelected(formattedUri);
          Alert.alert("Success", "Photo captured successfully!");
        }
      } else {
        // Gallery/Camera options remain the same but add compression
        Alert.alert("Select Photo", "Choose how you want to select a photo", [
          {
            text: "Camera",
            onPress: async () => {
              const cameraPermission =
                await ImagePicker.requestCameraPermissionsAsync();
              if (!cameraPermission.granted) {
                Alert.alert(
                  "Permission Required",
                  "Camera permission is required."
                );
                return;
              }

              const result = await ImagePicker.launchCameraAsync({
                mediaTypes: ["images"],
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
              });

              if (
                !result.canceled &&
                result.assets &&
                result.assets.length > 0
              ) {
                const uri = result.assets[0].uri;
                const compressedUri = await compressImage(uri);
                const formattedUri = compressedUri.startsWith("file://")
                  ? compressedUri
                  : `file://${compressedUri}`;
                onPhotoSelected(formattedUri);
                Alert.alert("Success", "Photo captured!");
              }
            },
          },
          {
            text: "Gallery",
            onPress: async () => {
              const mediaPermission =
                await ImagePicker.requestMediaLibraryPermissionsAsync();
              if (!mediaPermission.granted) {
                Alert.alert(
                  "Permission Required",
                  "Media library permission is required."
                );
                return;
              }

              const result = await ImagePicker.launchImageLibraryAsync({
                mediaTypes: ["images"],
                allowsEditing: true,
                aspect: [4, 3],
                quality: 0.8,
              });

              if (
                !result.canceled &&
                result.assets &&
                result.assets.length > 0
              ) {
                const uri = result.assets[0].uri;
                const compressedUri = await compressImage(uri);
                const formattedUri = compressedUri.startsWith("file://")
                  ? compressedUri
                  : `file://${compressedUri}`;
                onPhotoSelected(formattedUri);
                Alert.alert("Success", "Photo selected!");
              }
            },
          },
          {
            text: "Cancel",
            style: "cancel",
          },
        ]);
      }
    } catch (error: any) {
      console.error("📸 Photo selection error:", error);
      Alert.alert("Error", `Failed to select photo: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const removePhoto = () => {
    Alert.alert("Remove Photo", "Are you sure you want to remove this photo?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Remove",
        style: "destructive",
        onPress: () => onPhotoSelected(""),
      },
    ]);
  };

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {title}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      <TouchableOpacity
        style={[styles.uploadContainer, error ? styles.uploadError : null]}
        onPress={selectPhoto}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator size="large" color="#007AFF" />
        ) : value ? (
          <View style={styles.imageContainer}>
            <Image source={{ uri: value }} style={styles.image} />
            <TouchableOpacity style={styles.removeButton} onPress={removePhoto}>
              <Ionicons name="close-circle" size={24} color="#FF3B30" />
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.placeholderContainer}>
            <Ionicons name="camera" size={40} color="#007AFF" />
            <Text style={styles.placeholderText}>
              {cameraOnly ? "Tap to take photo" : "Tap to select photo"}
            </Text>
            {!cameraOnly && (
              <Text style={styles.placeholderSubtext}>Camera or Gallery</Text>
            )}
          </View>
        )}
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  labelContainer: {
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  required: {
    color: "#FF3B30",
  },
  subtitle: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
  },
  uploadContainer: {
    borderWidth: 2,
    borderColor: "#E5E5E7",
    borderStyle: "dashed",
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 120,
    backgroundColor: "#F8F9FA",
  },
  uploadError: {
    borderColor: "#FF3B30",
  },
  imageContainer: {
    position: "relative",
    width: "100%",
    alignItems: "center",
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removeButton: {
    position: "absolute",
    top: -8,
    right: "35%",
    backgroundColor: "white",
    borderRadius: 12,
  },
  placeholderContainer: {
    alignItems: "center",
  },
  placeholderText: {
    marginTop: 8,
    fontSize: 14,
    color: "#666",
  },
  placeholderSubtext: {
    marginTop: 4,
    fontSize: 12,
    color: "#999",
  },
  errorText: {
    marginTop: 4,
    fontSize: 12,
    color: "#FF3B30",
  },
});
