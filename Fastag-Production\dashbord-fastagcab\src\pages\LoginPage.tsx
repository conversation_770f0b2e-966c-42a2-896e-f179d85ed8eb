import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { useAuth } from '../contexts/AuthContext'
import { Navigate } from 'react-router-dom'
import Button from '../components/ui/Button'
import { Eye, EyeOff, Shield } from 'lucide-react'

interface LoginForm {
  phoneNumber: string
  password: string
}

const LoginPage: React.FC = () => {
  const { login, user } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginForm>()

  // Redirect if already logged in
  if (user) {
    return <Navigate to="/" replace />
  }

  const onSubmit = async (data: LoginForm) => {
    setIsLoading(true)
    const success = await login(data.phoneNumber, data.password)
    setIsLoading(false)
    
    if (success) {
      // Navigation will be handled by the auth context
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-primary-100 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-48 w-48 bg-white rounded-full flex items-center justify-center">
            {/* <Shield className="h-8 w-8 text-white" /> */}
            <img src="fastagcab.png" className="h-24 w-24 text-white" alt="" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Admin Dashboard
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Sign in to access FASTAGCAB admin panel
          </p>
        </div>
        
        <div className="bg-white py-8 px-6 shadow-medium rounded-lg">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <label htmlFor="phoneNumber" className="label text-gray-700">
                Phone Number
              </label>
              <div className="mt-1">
                <input
                  {...register('phoneNumber', {
                    required: 'Phone number is required',
                    pattern: {
                      value: /^[6-9]\d{9}$/,
                      message: 'Please enter a valid 10-digit phone number'
                    }
                  })}
                  type="tel"
                  className="input"
                  placeholder="Enter your phone number"
                  defaultValue="8959305283"
                />
                {errors.phoneNumber && (
                  <p className="mt-1 text-sm text-danger-600">
                    {errors.phoneNumber.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="password" className="label text-gray-700">
                Password
              </label>
              <div className="mt-1 relative">
                <input
                  {...register('password', {
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters'
                    }
                  })}
                  type={showPassword ? 'text' : 'password'}
                  className="input pr-10"
                  placeholder="Enter your password"
                  defaultValue="123456789"
                />
                <button type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-400" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-400" />
                  )}
                </button>
                {errors.password && (
                  <p className="mt-1 text-sm text-danger-600">
                    {errors.password.message}
                  </p>
                )}
              </div>
            </div>

            <div>
              <Button
                type="submit"
                className="w-full"
                loading={isLoading} // Ensure your Button component handles this
                size="lg"
              >
                Sign In
              </Button>
            </div>
          </form>
          
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              Admin access only. Contact system administrator for access.
            </p>
            <div className="mt-3 p-3 bg-blue-50 rounded-md">
              <p className="text-xs text-blue-600 font-medium">Test Credentials:</p>
              <p className="text-xs text-blue-600">Phone: 8959305283</p>
              <p className="text-xs text-blue-600">Password: 123456789</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LoginPage

