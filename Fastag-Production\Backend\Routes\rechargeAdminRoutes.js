import express from 'express';
import {
  getAllRechargesForAdmin,
  updateRechargeStatusByAdmin
} from '../controllers/rechargeAdminController.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// GET all recharges for admin
router.get('/admin', authenticateToken, requireAdmin, getAllRechargesForAdmin);

// PATCH update recharge status
router.patch('/admin/:id', authenticateToken, requireAdmin, updateRechargeStatusByAdmin);

export default router;
