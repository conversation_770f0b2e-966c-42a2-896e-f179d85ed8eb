import User from '../Models/user.js';
import QRCode from '../Models/QRCode.js';
import PointsService from '../services/pointsService.js';
import scheduledTaskService from '../services/scheduledTaskService.js';
import mongoose from 'mongoose';

// @desc    Get dashboard statistics
// @route   GET api/admin/dashboard-stats
// @access  Private (Admin only)
export const getDashboardStats = async (req, res) => {
    try {
        // Get total counts
        const totalUsers = await User.countDocuments();
        const totalElectricians = await User.countDocuments({ role: 'Electrician' });
        const totalDistributors = await User.countDocuments({ role: 'Distributor' });
        const pendingApprovals = await User.countDocuments({ status: 'pending' });
        
        // Get total points
        const pointsAggregation = await User.aggregate([
            {
                $group: {
                    _id: null,
                    totalMonthlyPoints: { $sum: '$monthlyPoints' },
                    totalYearlyPoints: { $sum: '$yearlyPoints' }
                }
            }
        ]);
        
        const totalPoints = pointsAggregation[0] || { totalMonthlyPoints: 0, totalYearlyPoints: 0 };
        
        // Get recent registrations (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        const recentRegistrations = await User.countDocuments({
            createdAt: { $gte: thirtyDaysAgo }
        });
        
        // Calculate growth rate (compare with previous 30 days)
        const sixtyDaysAgo = new Date();
        sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
        
        const previousPeriodRegistrations = await User.countDocuments({
            createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo }
        });
        
        const growthRate = previousPeriodRegistrations > 0 
            ? ((recentRegistrations - previousPeriodRegistrations) / previousPeriodRegistrations * 100).toFixed(1)
            : 0;

        res.status(200).json({
            success: true,
            data: {
                totalUsers,
                totalElectricians,
                totalDistributors,
                pendingApprovals,
                totalMonthlyPoints: totalPoints.totalMonthlyPoints,
                totalYearlyPoints: totalPoints.totalYearlyPoints,
                recentRegistrations,
                growthRate: parseFloat(growthRate)
            }
        });

    } catch (error) {
        console.error('Dashboard stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching dashboard stats'
        });
    }
};

// @desc    Get user analytics data
// @route   GET api/admin/user-analytics
// @access  Private (Admin only)
export const getUserAnalytics = async (req, res) => {
    try {
        const { period = '6m' } = req.query;
        
        let startDate = new Date();
        let groupBy = '$month';
        
        // Set date range based on period
        switch (period) {
            case '7d':
                startDate.setDate(startDate.getDate() - 7);
                groupBy = '$dayOfYear';
                break;
            case '30d':
                startDate.setDate(startDate.getDate() - 30);
                groupBy = '$dayOfMonth';
                break;
            case '6m':
                startDate.setMonth(startDate.getMonth() - 6);
                groupBy = '$month';
                break;
            case '1y':
                startDate.setFullYear(startDate.getFullYear() - 1);
                groupBy = '$month';
                break;
        }

        // Get user registrations over time
        const userRegistrations = await User.aggregate([
            {
                $match: {
                    createdAt: { $gte: startDate }
                }
            },
            {
                $group: {
                    _id: {
                        period: { [groupBy]: '$createdAt' },
                        year: { $year: '$createdAt' },
                        role: '$role'
                    },
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { '_id.year': 1, '_id.period': 1 }
            }
        ]);

        // Get user status distribution
        const statusDistribution = await User.aggregate([
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);

        res.status(200).json({
            success: true,
            data: {
                registrations: userRegistrations,
                statusDistribution
            }
        });

    } catch (error) {
        console.error('User analytics error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching user analytics'
        });
    }
};

// @desc    Get points analytics data
// @route   GET api/admin/points-analytics
// @access  Private (Admin only)
export const getPointsAnalytics = async (req, res) => {
    try {
        const { period = '6m' } = req.query;
        
        // Get points distribution by user role
        const pointsByRole = await User.aggregate([
            {
                $group: {
                    _id: '$role',
                    totalMonthlyPoints: { $sum: '$monthlyPoints' },
                    totalYearlyPoints: { $sum: '$yearlyPoints' },
                    averageMonthlyPoints: { $avg: '$monthlyPoints' },
                    userCount: { $sum: 1 }
                }
            }
        ]);

        // Get top users by points
        const topUsers = await User.find()
            .select('fullName phoneNumber role monthlyPoints yearlyPoints')
            .sort({ yearlyPoints: -1 })
            .limit(10);

        // Get points distribution ranges
        const pointsRanges = await User.aggregate([
            {
                $bucket: {
                    groupBy: '$monthlyPoints',
                    boundaries: [0, 100, 500, 1000, 5000, 10000],
                    default: '10000+',
                    output: {
                        count: { $sum: 1 },
                        users: { $push: '$fullName' }
                    }
                }
            }
        ]);

        res.status(200).json({
            success: true,
            data: {
                pointsByRole,
                topUsers,
                pointsRanges
            }
        });

    } catch (error) {
        console.error('Points analytics error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching points analytics'
        });
    }
};

// @desc    Get QR code statistics
// @route   GET api/admin/qr-stats
// @access  Private (Admin only)
export const getQRCodeStats = async (req, res) => {
    try {
        // Check if QRCode model exists, if not return mock data
        let qrStats = {
            totalQRCodes: 0,
            availableQRCodes: 0,
            redeemedQRCodes: 0,
            totalPointsDistributed: 0
        };

        try {
            const totalQRCodes = await QRCode.countDocuments();
            const availableQRCodes = await QRCode.countDocuments({ status: 'Not Redeem' });
            const redeemedQRCodes = await QRCode.countDocuments({ status: 'Redeemed' });
            
            const pointsAggregation = await QRCode.aggregate([
                {
                    $group: {
                        _id: null,
                        totalPoints: { $sum: '$points' }
                    }
                }
            ]);
            
            const totalPointsDistributed = pointsAggregation[0]?.totalPoints || 0;

            qrStats = {
                totalQRCodes,
                availableQRCodes,
                redeemedQRCodes,
                totalPointsDistributed
            };
        } catch (qrError) {
            // QRCode collection might not exist yet, return default stats
            console.log('QRCode collection not found, returning default stats');
        }

        res.status(200).json({
            success: true,
            data: qrStats
        });

    } catch (error) {
        console.error('QR stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching QR stats'
        });
    }
};

// @desc    Bulk update user status
// @route   POST api/admin/bulk-update-status
// @access  Private (Admin only)
export const bulkUpdateUserStatus = async (req, res) => {
    try {
        const { userIds, status } = req.body;

        if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'User IDs array is required'
            });
        }

        if (!['pending', 'approved', 'rejected'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid status. Must be pending, approved, or rejected'
            });
        }

        const result = await User.updateMany(
            { _id: { $in: userIds } },
            { status: status }
        );

        res.status(200).json({
            success: true,
            message: `Successfully updated ${result.modifiedCount} users`,
            data: {
                matchedCount: result.matchedCount,
                modifiedCount: result.modifiedCount
            }
        });

    } catch (error) {
        console.error('Bulk update error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while updating users'
        });
    }
};

// @desc    Export users data
// @route   GET api/admin/export-users
// @access  Private (Admin only)
export const exportUsers = async (req, res) => {
    try {
        const { format = 'json' } = req.query;

        const users = await User.find()
            .select('-password')
            .sort({ createdAt: -1 });

        if (format === 'csv') {
            // Convert to CSV format
            const csvHeader = 'Name,Phone,Role,Status,City,State,Monthly Points,Yearly Points,Created At\n';
            const csvData = users.map(user => 
                `"${user.fullName}","${user.phoneNumber}","${user.role}","${user.status}","${user.city}","${user.state}",${user.monthlyPoints || 0},${user.yearlyPoints || 0},"${user.createdAt}"`
            ).join('\n');

            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', 'attachment; filename=users.csv');
            res.send(csvHeader + csvData);
        } else {
            // Return JSON format
            res.status(200).json({
                success: true,
                data: users,
                count: users.length,
                exportedAt: new Date().toISOString()
            });
        }

    } catch (error) {
        console.error('Export users error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while exporting users'
        });
    }
};

// @desc    Get system health status
// @route   GET api/admin/system-health
// @access  Private (Admin only)
export const getSystemHealth = async (req, res) => {
    try {
        // Database connection status
        const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
        
        // Memory usage
        const memoryUsage = process.memoryUsage();
        
        // Uptime
        const uptime = process.uptime();
        
        // Database stats
        const dbStats = await mongoose.connection.db.stats();
        
        res.status(200).json({
            success: true,
            data: {
                database: {
                    status: dbStatus,
                    name: mongoose.connection.name,
                    collections: dbStats.collections,
                    dataSize: dbStats.dataSize,
                    storageSize: dbStats.storageSize
                },
                server: {
                    uptime: uptime,
                    memoryUsage: {
                        rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
                        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
                        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB'
                    },
                    nodeVersion: process.version,
                    platform: process.platform
                },
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('System health error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching system health'
        });
    }
};

// @desc    Manually trigger monthly points reset
// @route   POST api/admin/trigger-monthly-reset
// @access  Private (Admin only)
export const triggerMonthlyReset = async (req, res) => {
    try {
        console.log('🔧 Admin triggered monthly reset');

        const result = await PointsService.processMonthlyResetForAllUsers();

        res.status(200).json({
            success: true,
            message: 'Monthly reset completed successfully',
            data: result
        });

    } catch (error) {
        console.error('Manual monthly reset error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to trigger monthly reset',
            error: error.message
        });
    }
};

// @desc    Manually trigger yearly points reset
// @route   POST api/admin/trigger-yearly-reset
// @access  Private (Admin only)
export const triggerYearlyReset = async (req, res) => {
    try {
        console.log('🔧 Admin triggered yearly reset check');

        const result = await PointsService.processYearlyResetForAllUsers();

        res.status(200).json({
            success: true,
            message: 'Yearly reset check completed successfully',
            data: result
        });

    } catch (error) {
        console.error('Manual yearly reset error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to trigger yearly reset',
            error: error.message
        });
    }
};

// @desc    Get scheduled tasks status
// @route   GET api/admin/scheduled-tasks-status
// @access  Private (Admin only)
export const getScheduledTasksStatus = async (req, res) => {
    try {
        const status = scheduledTaskService.getStatus();

        res.status(200).json({
            success: true,
            message: 'Scheduled tasks status retrieved successfully',
            data: status
        });

    } catch (error) {
        console.error('Scheduled tasks status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get scheduled tasks status',
            error: error.message
        });
    }
};

// @desc    Get points system overview
// @route   GET api/admin/points-system-overview
// @access  Private (Admin only)
export const getPointsSystemOverview = async (req, res) => {
    try {
        // Get users needing monthly reset
        const usersNeedingMonthlyReset = await User.countDocuments({
            $or: [
                { lastMonthlyReset: null },
                { lastMonthlyReset: { $lt: new Date(new Date().getFullYear(), new Date().getMonth(), 1) } }
            ]
        });

        // Get users needing yearly reset
        const usersNeedingYearlyReset = await User.countDocuments({
            yearlyPointsResetAt: { $lte: new Date() }
        });

        // Get total points in system
        const pointsAggregation = await User.aggregate([
            {
                $group: {
                    _id: null,
                    totalMonthlyPoints: { $sum: '$monthlyPoints' },
                    totalYearlyPoints: { $sum: '$yearlyPoints' },
                    avgMonthlyPoints: { $avg: '$monthlyPoints' },
                    avgYearlyPoints: { $avg: '$yearlyPoints' },
                    userCount: { $sum: 1 }
                }
            }
        ]);

        const pointsStats = pointsAggregation[0] || {
            totalMonthlyPoints: 0,
            totalYearlyPoints: 0,
            avgMonthlyPoints: 0,
            avgYearlyPoints: 0,
            userCount: 0
        };

        // Get recent point activities
        const recentActivities = await PointHistory.find()
            .populate('userId', 'fullName phoneNumber')
            .sort({ createdAt: -1 })
            .limit(10)
            .select('transactionType pointsChange source description createdAt userId');

        res.status(200).json({
            success: true,
            message: 'Points system overview retrieved successfully',
            data: {
                resetStatus: {
                    usersNeedingMonthlyReset,
                    usersNeedingYearlyReset
                },
                pointsStats,
                recentActivities,
                scheduledTasks: scheduledTaskService.getStatus()
            }
        });

    } catch (error) {
        console.error('Points system overview error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get points system overview',
            error: error.message
        });
    }
};
