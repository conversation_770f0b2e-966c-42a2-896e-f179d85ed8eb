import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  ActivityIndicator,
  Alert
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '../contexts/ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import getServerBaseUrl from '@/envConfig';
const BackendURL = getServerBaseUrl();

interface PointHistoryItem {
  _id: string;
  transactionType: 'earned' | 'redeemed' | 'adjusted' | 'expired' | 'bonus';
  pointsChange: number;
  pointsBalance: number;
  source: string;
  description: string;
  createdAt: string;
  metadata?: {
    qrCode?: string;
    productName?: string;
    giftName?: string;
    adminNote?: string;
  };
}

interface PointStats {
  currentPoints: {
    monthly: number;
    yearly: number;
  };
  periodStats: {
    earned?: { totalPoints: number; count: number; avgPoints: number };
    redeemed?: { totalPoints: number; count: number; avgPoints: number };
    adjusted?: { totalPoints: number; count: number; avgPoints: number };
  };
}

export default function PointHistoryScreen() {
  const router = useRouter();
  const { colors } = useTheme();
  
  const [history, setHistory] = useState<PointHistoryItem[]>([]);
  const [stats, setStats] = useState<PointStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');

  const backendUrl = BackendURL;

  const fetchPointHistory = async (pageNum = 1, filter = 'all', refresh = false) => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) return;

      if (refresh) {
        setRefreshing(true);
      } else if (pageNum === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const queryParams = new URLSearchParams({
        page: pageNum.toString(),
        limit: '20',
        sortOrder: 'desc'
      });

      if (filter !== 'all') {
        queryParams.append('transactionType', filter);
      }

      const response = await fetch(`${backendUrl}api/points/history?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        if (pageNum === 1 || refresh) {
          setHistory(data.data.history);
        } else {
          setHistory(prev => [...prev, ...data.data.history]);
        }
        setHasMore(data.data.pagination.hasNext);
      } else {
        Alert.alert('Error', data.message || 'Failed to fetch point history');
      }
    } catch (error) {
      console.error('Fetch point history error:', error);
      Alert.alert('Error', 'Failed to fetch point history');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  };

  const fetchPointStats = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      if (!token) return;

      const response = await fetch(`${backendUrl}api/points/stats?period=30d`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Fetch point stats error:', error);
    }
  };

  useEffect(() => {
    fetchPointHistory();
    fetchPointStats();
  }, []);

  const handleRefresh = () => {
    setPage(1);
    fetchPointHistory(1, selectedFilter, true);
    fetchPointStats();
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchPointHistory(nextPage, selectedFilter);
    }
  };

  const handleFilterChange = (filter: string) => {
    setSelectedFilter(filter);
    setPage(1);
    fetchPointHistory(1, filter);
  };

  const getTransactionIcon = (type: string, source: string) => {
    switch (type) {
      case 'earned':
        return source === 'qr_scan' ? 'qr-code' : 'add-circle';
      case 'redeemed':
        return 'gift';
      case 'adjusted':
        return 'settings';
      case 'bonus':
        return 'star';
      default:
        return 'help-circle';
    }
  };

  const getTransactionColor = (pointsChange: number) => {
    return pointsChange > 0 ? colors.success : colors.error;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderHistoryItem = ({ item }: { item: PointHistoryItem }) => (
    <View style={[styles.historyItem, { backgroundColor: colors.surface }]}>
      <View style={styles.historyItemLeft}>
        <View style={[
          styles.iconContainer,
          { backgroundColor: getTransactionColor(item.pointsChange) + '15' }
        ]}>
          <Ionicons
            name={getTransactionIcon(item.transactionType, item.source) as any}
            size={20}
            color={getTransactionColor(item.pointsChange)}
          />
        </View>
        <View style={styles.historyItemContent}>
          <Text style={[styles.historyItemTitle, { color: colors.text }]}>
            {item.description}
          </Text>
          <Text style={[styles.historyItemDate, { color: colors.textSecondary }]}>
            {formatDate(item.createdAt)}
          </Text>
          {item.metadata?.productName && (
            <Text style={[styles.historyItemMeta, { color: colors.textSecondary }]}>
              {item.metadata.productName}
            </Text>
          )}
        </View>
      </View>
      <View style={styles.historyItemRight}>
        <Text style={[
          styles.pointsChange,
          { color: getTransactionColor(item.pointsChange) }
        ]}>
          {item.pointsChange > 0 ? '+' : ''}{item.pointsChange}
        </Text>
        <Text style={[styles.pointsBalance, { color: colors.textSecondary }]}>
          Balance: {item.pointsBalance}
        </Text>
      </View>
    </View>
  );

  const renderFilterButton = (filter: string, label: string) => (
    <TouchableOpacity
      style={[
        styles.filterButton,
        {
          backgroundColor: selectedFilter === filter ? colors.primary : colors.surface,
          borderColor: colors.border
        }
      ]}
      onPress={() => handleFilterChange(filter)}
    >
      <Text style={[
        styles.filterButtonText,
        {
          color: selectedFilter === filter ? colors.background : colors.text
        }
      ]}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  const renderStatsCard = () => {
    if (!stats) return null;

    return (
      <View style={[styles.statsCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.statsTitle, { color: colors.text }]}>30-Day Summary</Text>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.success }]}>
              +{stats.periodStats.earned?.totalPoints || 0}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Earned ({stats.periodStats.earned?.count || 0})
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.error }]}>
              -{Math.abs(stats.periodStats.redeemed?.totalPoints || 0)}
            </Text>
            <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
              Redeemed ({stats.periodStats.redeemed?.count || 0})
            </Text>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Point History</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
            Loading point history...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Point History</Text>
      </View>

      <FlatList
        data={history}
        renderItem={renderHistoryItem}
        keyExtractor={(item) => item._id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        ListHeaderComponent={
          <View>
            {renderStatsCard()}
            
            {/* Filter Buttons */}
            <View style={styles.filterContainer}>
              {renderFilterButton('all', 'All')}
              {renderFilterButton('earned', 'Earned')}
              {renderFilterButton('redeemed', 'Redeemed')}
              {renderFilterButton('adjusted', 'Adjusted')}
            </View>
          </View>
        }
        ListFooterComponent={
          loadingMore ? (
            <View style={styles.loadingMore}>
              <ActivityIndicator size="small" color={colors.primary} />
            </View>
          ) : null
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="receipt-outline" size={64} color={colors.textSecondary} />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              No point history found
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
  },
  listContainer: {
    padding: 16,
  },
  statsCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  filterContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  historyItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  historyItemContent: {
    flex: 1,
  },
  historyItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  historyItemDate: {
    fontSize: 12,
    marginBottom: 2,
  },
  historyItemMeta: {
    fontSize: 12,
    fontStyle: 'italic',
  },
  historyItemRight: {
    alignItems: 'flex-end',
  },
  pointsChange: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  pointsBalance: {
    fontSize: 12,
    marginTop: 4,
  },
  loadingMore: {
    padding: 16,
    alignItems: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 16,
  },
});
