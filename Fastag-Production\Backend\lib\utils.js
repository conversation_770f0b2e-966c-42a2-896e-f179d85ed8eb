import jwt from "jsonwebtoken";

export const generateToken = (userId, res) => {
  const token = jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: "7d",
  });

  res.cookie("jwt", token, {
    maxAge: 7 * 24 * 60 * 60 * 1000, // MS
    httpOnly: true, // Prevent XSS attacks
    sameSite: "None",  // CSRF protection
    secure: process.env.NODE_ENV !== "development", // true for production, false for dev
  });
  
  return token;
};
 