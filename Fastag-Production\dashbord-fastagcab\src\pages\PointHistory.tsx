import React, { useState, useEffect } from 'react'
import { Search, Filter, Download, TrendingUp, TrendingDown, Award, Calendar, User, Eye } from 'lucide-react'
import Card, { CardHeader, CardTitle, CardContent } from '../components/ui/Card'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import toast from 'react-hot-toast'

interface PointHistoryItem {
  _id: string
  userId: {
    _id: string
    fullName: string
    phoneNumber: string
    dealerCode: string
    role: string
  }
  transactionType: 'earned' | 'redeemed' | 'adjusted' | 'expired' | 'bonus'
  pointsChange: number
  pointsBalance: number
  source: string
  description: string
  createdAt: string
  metadata?: {
    qrCode?: string
    productName?: string
    giftName?: string
    adminNote?: string
    adminId?: {
      fullName: string
    }
  }
}

interface PointStats {
  transactionStats: Array<{
    _id: string
    totalPoints: number
    count: number
    avgPoints: number
  }>
  sourceStats: Array<{
    _id: string
    totalPoints: number
    count: number
  }>
  dailyTrends: Array<{
    _id: { year: number; month: number; day: number }
    totalPoints: number
    earnedPoints: number
    redeemedPoints: number
    transactionCount: number
  }>
}

export default function PointHistory() {
  const [history, setHistory] = useState<PointHistoryItem[]>([])
  const [stats, setStats] = useState<PointStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  
  // Filters
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTransactionType, setSelectedTransactionType] = useState('')
  const [selectedSource, setSelectedSource] = useState('')
  const [selectedUserId, setSelectedUserId] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [period, setPeriod] = useState('30d')

  // Modal state
  const [selectedItem, setSelectedItem] = useState<PointHistoryItem | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)

  const fetchPointHistory = async (page = 1) => {
    try {
      setLoading(true)
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: '20',
        sortOrder: 'desc'
      })

      if (selectedTransactionType) queryParams.append('transactionType', selectedTransactionType)
      if (selectedSource) queryParams.append('source', selectedSource)
      if (selectedUserId) queryParams.append('userId', selectedUserId)
      if (startDate) queryParams.append('startDate', startDate)
      if (endDate) queryParams.append('endDate', endDate)

      const response = await fetch(`api/points/history/all?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        setHistory(data.data.history)
        setCurrentPage(data.data.pagination.currentPage)
        setTotalPages(data.data.pagination.totalPages)
        setTotalCount(data.data.pagination.totalCount)
      } else {
        toast.error(data.message || 'Failed to fetch point history')
      }
    } catch (error) {
      console.error('Fetch point history error:', error)
      toast.error('Failed to fetch point history')
    } finally {
      setLoading(false)
    }
  }

  const fetchPointStats = async () => {
    try {
      const response = await fetch(`api/points/analytics?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        setStats(data.data)
      }
    } catch (error) {
      console.error('Fetch point stats error:', error)
    }
  }

  useEffect(() => {
    fetchPointHistory()
    fetchPointStats()
  }, [selectedTransactionType, selectedSource, selectedUserId, startDate, endDate])

  useEffect(() => {
    fetchPointStats()
  }, [period])

  const handleSearch = () => {
    setCurrentPage(1)
    fetchPointHistory(1)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchPointHistory(page)
  }

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'earned': return 'text-green-600 bg-green-100'
      case 'redeemed': return 'text-red-600 bg-red-100'
      case 'adjusted': return 'text-blue-600 bg-blue-100'
      case 'bonus': return 'text-purple-600 bg-purple-100'
      case 'expired': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'qr_scan': return '📱'
      case 'gift_redemption': return '🎁'
      case 'admin_adjustment': return '⚙️'
      case 'bonus_reward': return '⭐'
      case 'referral': return '👥'
      default: return '📋'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const exportData = async () => {
    try {
      const queryParams = new URLSearchParams({
        limit: '10000', // Large limit for export
        sortOrder: 'desc'
      })

      if (selectedTransactionType) queryParams.append('transactionType', selectedTransactionType)
      if (selectedSource) queryParams.append('source', selectedSource)
      if (selectedUserId) queryParams.append('userId', selectedUserId)
      if (startDate) queryParams.append('startDate', startDate)
      if (endDate) queryParams.append('endDate', endDate)

      const response = await fetch(`api/points/history/all?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (data.success) {
        // Convert to CSV
        const csvContent = [
          ['Date', 'User', 'Phone', 'Dealer Code', 'Transaction Type', 'Points Change', 'Balance', 'Source', 'Description'].join(','),
          ...data.data.history.map((item: PointHistoryItem) => [
            formatDate(item.createdAt),
            item.userId.fullName,
            item.userId.phoneNumber,
            item.userId.dealerCode,
            item.transactionType,
            item.pointsChange,
            item.pointsBalance,
            item.source,
            `"${item.description}"`
          ].join(','))
        ].join('\n')

        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `point-history-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)

        toast.success('Point history exported successfully')
      } else {
        toast.error('Failed to export data')
      }
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export data')
    }
  }

  if (loading && history.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Point History</h1>
          <p className="text-gray-600">Track all point transactions and user activities</p>
        </div>
        <button
          onClick={exportData}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          <Download className="h-4 w-4" />
          Export CSV
        </button>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {stats.transactionStats.map((stat) => (
            <Card key={stat._id}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`p-3 rounded-lg ${getTransactionTypeColor(stat._id).split(' ')[1]}`}>
                    {stat._id === 'earned' && <TrendingUp className={`h-6 w-6 ${getTransactionTypeColor(stat._id).split(' ')[0]}`} />}
                    {stat._id === 'redeemed' && <TrendingDown className={`h-6 w-6 ${getTransactionTypeColor(stat._id).split(' ')[0]}`} />}
                    {stat._id === 'adjusted' && <Award className={`h-6 w-6 ${getTransactionTypeColor(stat._id).split(' ')[0]}`} />}
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600 capitalize">{stat._id}</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stat._id === 'redeemed' ? Math.abs(stat.totalPoints).toLocaleString() : stat.totalPoints.toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-500">{stat.count} transactions</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <select
              value={selectedTransactionType}
              onChange={(e) => setSelectedTransactionType(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              <option value="earned">Earned</option>
              <option value="redeemed">Redeemed</option>
              <option value="adjusted">Adjusted</option>
              <option value="bonus">Bonus</option>
              <option value="expired">Expired</option>
            </select>

            <select
              value={selectedSource}
              onChange={(e) => setSelectedSource(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Sources</option>
              <option value="qr_scan">QR Scan</option>
              <option value="gift_redemption">Gift Redemption</option>
              <option value="admin_adjustment">Admin Adjustment</option>
              <option value="bonus_reward">Bonus Reward</option>
              <option value="referral">Referral</option>
            </select>

            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Start Date"
            />

            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="End Date"
            />

            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>

            <button
              onClick={handleSearch}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center gap-2 transition-colors"
            >
              <Search className="h-4 w-4" />
              Search
            </button>
          </div>
        </CardContent>
      </Card>

      {/* Point History Table */}
      <Card>
        <CardHeader>
          <CardTitle>Point History ({totalCount.toLocaleString()} records)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Transaction
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Points
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Balance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Source
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {history.map((item) => (
                  <tr key={item._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(item.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {item.userId.fullName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {item.userId.phoneNumber} • {item.userId.dealerCode}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTransactionTypeColor(item.transactionType)}`}>
                        {item.transactionType}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <span className={item.pointsChange > 0 ? 'text-green-600' : 'text-red-600'}>
                        {item.pointsChange > 0 ? '+' : ''}{item.pointsChange}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.pointsBalance}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex items-center">
                        <span className="mr-2">{getSourceIcon(item.source)}</span>
                        {item.source.replace('_', ' ')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => {
                          setSelectedItem(item)
                          setShowDetailsModal(true)
                        }}
                        className="text-blue-600 hover:text-blue-900 flex items-center gap-1"
                      >
                        <Eye className="h-4 w-4" />
                        View
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-700">
                Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, totalCount)} of {totalCount} results
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        page === currentPage
                          ? 'text-blue-600 bg-blue-50 border border-blue-300'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  )
                })}
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Details Modal */}
      {showDetailsModal && selectedItem && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Transaction Details
                </h3>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">User</label>
                  <p className="text-sm text-gray-900">{selectedItem.userId.fullName}</p>
                  <p className="text-xs text-gray-500">{selectedItem.userId.phoneNumber} • {selectedItem.userId.dealerCode}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Transaction Type</label>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTransactionTypeColor(selectedItem.transactionType)}`}>
                    {selectedItem.transactionType}
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Points Change</label>
                  <p className={`text-lg font-bold ${selectedItem.pointsChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {selectedItem.pointsChange > 0 ? '+' : ''}{selectedItem.pointsChange}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Balance After</label>
                  <p className="text-sm text-gray-900">{selectedItem.pointsBalance}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Source</label>
                  <p className="text-sm text-gray-900">{selectedItem.source.replace('_', ' ')}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <p className="text-sm text-gray-900">{selectedItem.description}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">Date & Time</label>
                  <p className="text-sm text-gray-900">{formatDate(selectedItem.createdAt)}</p>
                </div>

                {selectedItem.metadata && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Additional Details</label>
                    <div className="text-sm text-gray-900 space-y-1">
                      {selectedItem.metadata.productName && (
                        <p><strong>Product:</strong> {selectedItem.metadata.productName}</p>
                      )}
                      {selectedItem.metadata.qrCode && (
                        <p><strong>QR Code:</strong> {selectedItem.metadata.qrCode}</p>
                      )}
                      {selectedItem.metadata.giftName && (
                        <p><strong>Gift:</strong> {selectedItem.metadata.giftName}</p>
                      )}
                      {selectedItem.metadata.adminNote && (
                        <p><strong>Admin Note:</strong> {selectedItem.metadata.adminNote}</p>
                      )}
                      {selectedItem.metadata.adminId && (
                        <p><strong>Admin:</strong> {selectedItem.metadata.adminId.fullName}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end mt-6">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
