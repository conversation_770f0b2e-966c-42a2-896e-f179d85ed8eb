import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from './Models/user.js';
import GiftRedemption from './Models/GiftRedemption.js';
import PointHistory from './Models/PointHistory.js';
import PointsService from './services/pointsService.js';

// Load environment variables
dotenv.config();

/**
 * Debug script to check gift redemption points deduction
 */
async function debugGiftRedemption() {
    try {
        console.log('🔍 Debugging Gift Redemption Points Deduction...\n');

        // Connect to database
        await mongoose.connect(process.env.MONGO_URI);
        console.log('✅ Connected to database');

        // Find a user with points
        const usersWithPoints = await User.find({
            $and: [
                { monthlyPoints: { $gt: 0 } },
                { yearlyPoints: { $gt: 0 } }
            ]
        }).limit(5);

        if (usersWithPoints.length === 0) {
            console.log('❌ No users found with points. Creating test user...');
            
            const testUser = new User({
                fullName: 'Debug Test User',
                phoneNumber: 'debug_test_user_123',
                email: '<EMAIL>',
                password: 'testpassword123',
                role: 'user',
                isVerified: true,
                monthlyPoints: 2000,
                yearlyPoints: 3000
            });
            await testUser.save();
            usersWithPoints.push(testUser);
            console.log('✅ Created test user with 2000 monthly and 3000 yearly points');
        }

        console.log(`\n📊 Found ${usersWithPoints.length} users with points:`);
        usersWithPoints.forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.fullName} (${user.phoneNumber})`);
            console.log(`      Monthly: ${user.monthlyPoints}, Yearly: ${user.yearlyPoints}`);
        });

        // Select first user for testing
        const testUser = usersWithPoints[0];
        console.log(`\n👤 Testing with user: ${testUser.fullName}`);
        console.log(`   Initial Points - Monthly: ${testUser.monthlyPoints}, Yearly: ${testUser.yearlyPoints}`);

        // Create a test gift redemption
        console.log('\n🎁 Creating test gift redemption...');
        const testRedemption = new GiftRedemption({
            userId: testUser._id,
            productId: 'DEBUG_GIFT_001',
            productName: 'Debug Test Gift',
            productImage: 'https://example.com/debug-gift.jpg',
            pointsRequired: 500,
            userYearlyPointsAtRedemption: testUser.yearlyPoints,
            status: 'pending',
            redemptionDate: new Date(),
            metadata: {
                isRechargeProduct: false,
                debugTest: true
            }
        });
        await testRedemption.save();
        console.log('✅ Test redemption created');

        // Test direct PointsService deduction
        console.log('\n🔧 Testing direct PointsService.deductPoints...');
        
        const beforeDeduction = await User.findById(testUser._id);
        console.log(`   Before: Monthly=${beforeDeduction.monthlyPoints}, Yearly=${beforeDeduction.yearlyPoints}`);

        try {
            const result = await PointsService.deductPoints(
                testUser._id,
                500,
                'gift_redemption',
                {
                    giftName: 'Debug Test Gift',
                    sourceId: testRedemption._id,
                    sourceModel: 'GiftRedemption'
                }
            );

            console.log('✅ PointsService deduction successful:');
            console.log(`   Points Deducted: ${result.pointsDeducted}`);
            console.log(`   New Monthly Balance: ${result.newMonthlyBalance}`);
            console.log(`   New Yearly Balance: ${result.newYearlyBalance}`);

            // Verify in database
            const afterDeduction = await User.findById(testUser._id);
            console.log(`\n🔍 Database verification:`);
            console.log(`   After: Monthly=${afterDeduction.monthlyPoints}, Yearly=${afterDeduction.yearlyPoints}`);

            // Check if both were deducted
            const monthlyDeducted = beforeDeduction.monthlyPoints - afterDeduction.monthlyPoints;
            const yearlyDeducted = beforeDeduction.yearlyPoints - afterDeduction.yearlyPoints;

            console.log(`\n📊 Deduction Analysis:`);
            console.log(`   Monthly Points Deducted: ${monthlyDeducted} (Expected: 500)`);
            console.log(`   Yearly Points Deducted: ${yearlyDeducted} (Expected: 500)`);

            if (monthlyDeducted === 500 && yearlyDeducted === 500) {
                console.log('✅ SUCCESS: Both monthly and yearly points deducted correctly!');
            } else {
                console.log('❌ ISSUE: Points not deducted correctly from both balances');
            }

            // Check point history
            const pointHistory = await PointHistory.findOne({
                userId: testUser._id,
                source: 'gift_redemption',
                pointsChange: -500
            }).sort({ createdAt: -1 });

            if (pointHistory) {
                console.log('\n📝 Point History Created:');
                console.log(`   Transaction Type: ${pointHistory.transactionType}`);
                console.log(`   Points Change: ${pointHistory.pointsChange}`);
                console.log(`   Description: ${pointHistory.description}`);
                console.log(`   Monthly Before: ${pointHistory.metadata?.monthlyPointsBefore}`);
                console.log(`   Monthly After: ${pointHistory.metadata?.monthlyPointsAfter}`);
                console.log(`   Yearly Before: ${pointHistory.metadata?.yearlyPointsBefore}`);
                console.log(`   Yearly After: ${pointHistory.metadata?.yearlyPointsAfter}`);
            } else {
                console.log('❌ No point history found');
            }

        } catch (error) {
            console.error('❌ PointsService deduction failed:', error.message);
            if (error.code === 'INSUFFICIENT_POINTS') {
                console.log('   This is expected if user doesn\'t have enough points');
                console.log(`   Required: 500, Available: Monthly=${beforeDeduction.monthlyPoints}, Yearly=${beforeDeduction.yearlyPoints}`);
            }
        }

        // Test User model method directly
        console.log('\n🔧 Testing User model deductPoints method directly...');
        
        const directTestUser = await User.findById(testUser._id);
        const beforeDirect = { 
            monthly: directTestUser.monthlyPoints, 
            yearly: directTestUser.yearlyPoints 
        };
        
        console.log(`   Before Direct: Monthly=${beforeDirect.monthly}, Yearly=${beforeDirect.yearly}`);
        
        directTestUser.deductPoints(100);
        await directTestUser.save();
        
        console.log(`   After Direct: Monthly=${directTestUser.monthlyPoints}, Yearly=${directTestUser.yearlyPoints}`);
        
        const directMonthlyDeducted = beforeDirect.monthly - directTestUser.monthlyPoints;
        const directYearlyDeducted = beforeDirect.yearly - directTestUser.yearlyPoints;
        
        console.log(`   Direct Deduction: Monthly=${directMonthlyDeducted}, Yearly=${directYearlyDeducted}`);
        
        if (directMonthlyDeducted === 100 && directYearlyDeducted === 100) {
            console.log('✅ User model method works correctly');
        } else {
            console.log('❌ User model method has issues');
        }

        // Summary
        console.log('\n📋 Debug Summary:');
        console.log('   1. PointsService.deductPoints() - Check above results');
        console.log('   2. User.deductPoints() method - Check above results');
        console.log('   3. Point history creation - Check above results');
        console.log('   4. Database persistence - Check above results');

        console.log('\n💡 If you\'re still seeing issues in gift redemption:');
        console.log('   1. Check if the gift redemption controller is using PointsService');
        console.log('   2. Verify the redemption approval process');
        console.log('   3. Check for any middleware that might interfere');
        console.log('   4. Look at the actual API response in the frontend');

        // Cleanup test data
        await GiftRedemption.deleteOne({ _id: testRedemption._id });
        if (testUser.phoneNumber === 'debug_test_user_123') {
            await User.deleteOne({ _id: testUser._id });
            console.log('\n🧹 Test data cleaned up');
        }

    } catch (error) {
        console.error('❌ Debug failed:', error);
    } finally {
        await mongoose.disconnect();
        console.log('✅ Disconnected from database');
    }
}

// Run the debug
debugGiftRedemption();
