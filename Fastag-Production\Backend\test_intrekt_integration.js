import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

// Test phone number (replace with your test number)
const TEST_PHONE = '8959305284';

async function testIntrektIntegration() {
  console.log('🧪 Starting Intrekt Integration Tests...\n');

  try {
    // Test 1: Connection Test
    console.log('📡 Test 1: Testing Intrekt API Connection...');
    const connectionTest = await fetch(`${BASE_URL}/api/auth/test-intrekt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        testType: 'connection'
      })
    });

    const connectionResult = await connectionTest.json();
    console.log('Connection Test Result:', JSON.stringify(connectionResult, null, 2));
    console.log('---\n');

    // Test 2: OTP Test
    console.log('📱 Test 2: Testing OTP Sending via Intrekt...');
    const otpTest = await fetch(`${BASE_URL}/api/auth/test-intrekt`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        testType: 'otp',
        phoneNumber: TEST_PHONE
      })
    });

    const otpResult = await otpTest.json();
    console.log('OTP Test Result:', JSON.stringify(otpResult, null, 2));
    console.log('---\n');

    // Test 3: Send OTP via Regular Endpoint
    console.log('🚀 Test 3: Testing Regular Send OTP Endpoint...');
    const sendOtpTest = await fetch(`${BASE_URL}/api/auth/send-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phoneNumber: TEST_PHONE
      })
    });

    const sendOtpResult = await sendOtpTest.json();
    console.log('Send OTP Result:', JSON.stringify(sendOtpResult, null, 2));
    console.log('---\n');

    // Test 4: Verify OTP
    if (sendOtpResult.success && sendOtpResult.data && sendOtpResult.data.testOtp) {
      console.log('✅ Test 4: Testing OTP Verification...');
      const verifyOtpTest = await fetch(`${BASE_URL}/api/auth/verify-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: TEST_PHONE,
          otp: sendOtpResult.data.testOtp
        })
      });

      const verifyOtpResult = await verifyOtpTest.json();
      console.log('Verify OTP Result:', JSON.stringify(verifyOtpResult, null, 2));
      console.log('---\n');
    }

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test Error:', error);
  }
}

// Run the tests
testIntrektIntegration();
