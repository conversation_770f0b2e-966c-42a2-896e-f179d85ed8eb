import express from 'express';
import { body } from 'express-validator';
import {
  createRedemption,
  getUserRedemptions,
  getPendingRedemptions,
  processRedemption
} from '../controllers/giftRedemptionController.js';
import {
  authenticateToken,
  requireAdmin
} from '../middleware/auth.js';
import GiftRedemption from '../Models/GiftRedemption.js';

const router = express.Router();

// Validation rules for redemption creation
const redemptionValidation = [
  body('productId')
    .notEmpty()
    .withMessage('Product ID is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Product ID must be between 1 and 50 characters'),

  body('productName')
    .notEmpty()
    .withMessage('Product name is required')
    .isLength({ min: 1, max: 200 })
    .withMessage('Product name must be between 1 and 200 characters')
    .trim(),

  body('productImage')
    .notEmpty()
    .withMessage('Product image URL is required')
    .isURL()
    .withMessage('Product image must be a valid URL'),

  body('pointsRequired')
    .isInt({ min: 1 })
    .withMessage('Points required must be a positive integer')
    .toInt(),

  body('productCategory')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Product category must be between 1 and 50 characters')
    .trim()
];

// Validation rules for processing redemption
const processRedemptionValidation = [
  body('action')
    .isIn(['approve', 'deny'])
    .withMessage('Action must be either "approve" or "deny"'),
  
  body('adminNotes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Admin notes cannot exceed 500 characters')
    .trim()
];

// @route   POST api/gifts/redeem
// @desc    Create a new gift redemption request
// @access  Private
router.post('/redeem', authenticateToken, redemptionValidation, createRedemption);

// @route   GET api/gifts/my-redemptions
// @desc    Get user's redemption history
// @access  Private
router.get('/my-redemptions', authenticateToken, getUserRedemptions);

// @route   GET api/gifts/pending
// @desc    Get pending redemptions for dashboard (Admin only)
// @access  Private (Admin only)
router.get('/pending', authenticateToken, requireAdmin, getPendingRedemptions);

// @route   PUT api/gifts/process/:id
// @desc    Process redemption request (approve/deny) (Admin only)
// @access  Private (Admin only)
router.put('/process/:id', authenticateToken, requireAdmin, processRedemptionValidation, processRedemption);

// @route   GET api/gifts/redemption/:id
// @desc    Get single redemption details
// @access  Private (Own redemption or Admin)
router.get('/redemption/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const isAdmin = req.user.role === 'admin';

    const redemption = await GiftRedemption.findById(id).populate('userId', 'fullName phoneNumber dealerCode');
    
    if (!redemption) {
      return res.status(404).json({
        success: false,
        message: 'Redemption not found'
      });
    }

    // Check if user owns this redemption or is admin
    if (!isAdmin && redemption.userId._id.toString() !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: redemption
    });

  } catch (error) {
    console.error('Get redemption details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch redemption details'
    });
  }
});

// @route   GET api/gifts/stats
// @desc    Get redemption statistics (Admin only)
// @access  Private (Admin only)
router.get('/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const stats = await Promise.all([
      GiftRedemption.countDocuments({ status: 'pending' }),
      GiftRedemption.countDocuments({ status: 'approved' }),
      GiftRedemption.countDocuments({ status: 'denied' }),
      GiftRedemption.countDocuments({ status: 'cancelled' }),
      GiftRedemption.aggregate([
        { $match: { status: 'approved' } },
        { $group: { _id: null, totalPoints: { $sum: '$pointsDeducted' } } }
      ])
    ]);

    const [pending, approved, denied, cancelled, totalPointsResult] = stats;
    const totalPointsRedeemed = totalPointsResult[0]?.totalPoints || 0;

    res.json({
      success: true,
      data: {
        pending,
        approved,
        denied,
        cancelled,
        total: pending + approved + denied + cancelled,
        totalPointsRedeemed
      }
    });

  } catch (error) {
    console.error('Get redemption stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch redemption statistics'
    });
  }
});

export default router;
