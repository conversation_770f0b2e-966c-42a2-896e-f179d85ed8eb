# Production Environment Variables for Render Deployment
# These should be set in Render's environment variables section

# Server Configuration
PORT=10000
NODE_ENV=production

# Database Configuration (MongoDB Atlas recommended for production)
MONGO_URI=mongodb+srv://username:<EMAIL>/electrician-app?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=FASTAGCABAPP-super-secret-production-key-change-this-in-production
JWT_EXPIRES_IN=7d

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Client URL (for CORS)
CLIENT_URL=https://your-frontend-domain.com
