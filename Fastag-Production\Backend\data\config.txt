# Application Configuration File
# This file contains various configuration settings

APP_NAME=Dynamic File Reader API
VERSION=1.0.0
ENVIRONMENT=development
DEBUG_MODE=true

# Database Settings
DB_HOST=localhost
DB_PORT=27017
DB_NAME=fileReaderDB

# API Settings
API_VERSION=v1
MAX_FILE_SIZE=10MB
SUPPORTED_FORMATS=json,csv,txt,xml

# Security Settings
ENABLE_CORS=true
RATE_LIMIT=100
SESSION_TIMEOUT=3600

# File Processing Settings
MAX_FILES_PER_REQUEST=50
CACHE_ENABLED=true
CACHE_DURATION=300

# Logging Settings
LOG_LEVEL=info
LOG_FILE=app.log
ENABLE_FILE_LOGGING=true

# Performance Settings
WORKER_THREADS=4
MEMORY_LIMIT=512MB
TIMEOUT=30000

# Feature Flags
ENABLE_FILE_VALIDATION=true
ENABLE_COMPRESSION=false
ENABLE_ENCRYPTION=false
