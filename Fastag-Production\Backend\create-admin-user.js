import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';
import User from './Models/user.js';

// Load environment variables
dotenv.config();

const createAdminUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ phoneNumber: 'admin123' });
    if (existingAdmin) {
      console.log('❌ Admin user already exists with phone number: admin123');
      console.log('   You can use these credentials to login:');
      console.log('   Phone: admin123');
      console.log('   Password: admin123');
      process.exit(0);
    }

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 12);

    // Create admin user
    const adminUser = new User({
      fullName: 'Admin User',
      phoneNumber: 'admin123',
      password: hashedPassword,
      email: '<EMAIL>',
      role: 'admin',
      status: 'approved',
      isVerified: true,
      monthlyPoints: 0,
      yearlyPoints: 0,
      dateOfBirth: new Date('1990-01-01'),
      age: 34,
      adharNumber: '123456789012',
      panCardNumber: '**********',
      pinCode: '110001',
      state: 'Delhi',
      city: 'New Delhi',
      address: 'Admin Office, New Delhi',
      dealerCode: 'ADMIN001'
    });

    await adminUser.save();
    console.log('✅ Admin user created successfully!');
    console.log('   Login credentials:');
    console.log('   Phone: admin123');
    console.log('   Password: admin123');
    console.log('   Role: admin');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
    process.exit(0);
  }
};

createAdminUser();
