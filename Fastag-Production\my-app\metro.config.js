const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Enable bundle splitting and optimization
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    mangle: {
      keep_fnames: true,
    },
    output: {
      ascii_only: true,
      quote_style: 3,
      wrap_iife: true,
    },
    sourceMap: false,
    toplevel: false,
    warnings: false,
    parse: {
      bare_returns: false,
    },
  },
};

// Optimize resolver for better performance
config.resolver = {
  ...config.resolver,
  platforms: ['ios', 'android', 'native', 'web'],
  alias: {
    '@': __dirname,
    '@/components': require('path').resolve(__dirname, 'components'),
    '@/constants': require('path').resolve(__dirname, 'constants'),
    '@/contexts': require('path').resolve(__dirname, 'contexts'),
    '@/hooks': require('path').resolve(__dirname, 'hooks'),
    '@/services': require('path').resolve(__dirname, 'services'),
    '@/i18n': require('path').resolve(__dirname, 'i18n'),
  },
};

// Add memory optimization
config.maxWorkers = 2;

module.exports = config;
