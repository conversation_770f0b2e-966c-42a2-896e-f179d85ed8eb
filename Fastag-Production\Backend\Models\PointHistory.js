import mongoose from 'mongoose';

const pointHistorySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  transactionType: {
    type: String,
    enum: ['earned', 'redeemed', 'adjusted', 'expired', 'bonus'],
    required: [true, 'Transaction type is required']
  },
  pointsChange: {
    type: Number,
    required: [true, 'Points change is required']
  },
  pointsBalance: {
    type: Number,
    required: [true, 'Points balance is required'],
    min: [0, 'Points balance cannot be negative']
  },
  source: {
    type: String,
    enum: ['qr_scan', 'gift_redemption', 'recharge', 'admin_adjustment', 'bonus_reward', 'referral', 'monthly_reset', 'yearly_reset'],
    required: [true, 'Source is required']
  },
  sourceId: {
    type: mongoose.Schema.Types.ObjectId,
    refPath: 'sourceModel'
  },
  sourceModel: {
    type: String,
    enum: ['QRCode', 'GiftRedemption', 'Recharge', 'User', 'Referral']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  metadata: {
    qrCode: String,
    productName: String,
    giftName: String,
    adminId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    adminNote: String,
    ipAddress: String,
    userAgent: String,
    // New fields for enhanced points tracking
    monthlyPointsBefore: Number,
    yearlyPointsBefore: Number,
    monthlyPointsAfter: Number,
    yearlyPointsAfter: Number,
    pointType: {
      type: String,
      enum: ['monthly', 'yearly', 'both'],
      default: 'both'
    },
    resetDate: Date,
    expiredMonthlyPoints: Number,
    expiredYearlyPoints: Number,
    registrationAnniversary: Date,
    adjustmentType: {
      type: String,
      enum: ['monthly', 'yearly']
    }
  },
  status: {
    type: String,
    enum: ['completed', 'pending', 'failed', 'cancelled'],
    default: 'completed'
  },
  expiresAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
pointHistorySchema.index({ userId: 1, createdAt: -1 });
pointHistorySchema.index({ transactionType: 1, createdAt: -1 });
pointHistorySchema.index({ source: 1, createdAt: -1 });
pointHistorySchema.index({ status: 1, createdAt: -1 });

// Virtual for formatted points change
pointHistorySchema.virtual('formattedPointsChange').get(function() {
  return this.pointsChange > 0 ? `+${this.pointsChange}` : `${this.pointsChange}`;
});

// Virtual for transaction age
pointHistorySchema.virtual('transactionAge').get(function() {
  const now = new Date();
  const diffTime = Math.abs(now - this.createdAt);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
  return `${Math.ceil(diffDays / 365)} years ago`;
});

// Static method to get user's point history with pagination
pointHistorySchema.statics.getUserHistory = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    transactionType,
    source,
    startDate,
    endDate,
    sortBy = 'createdAt',
    sortOrder = -1
  } = options;

  const query = { userId };
  
  if (transactionType) query.transactionType = transactionType;
  if (source) query.source = source;
  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) query.createdAt.$gte = new Date(startDate);
    if (endDate) query.createdAt.$lte = new Date(endDate);
  }

  return this.find(query)
    .populate('sourceId')
    .populate('metadata.adminId', 'fullName')
    .sort({ [sortBy]: sortOrder })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .exec();
};

// Static method to get user's point statistics
pointHistorySchema.statics.getUserStats = function(userId, period = '30d') {
  const startDate = new Date();
  
  switch (period) {
    case '7d':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(startDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(startDate.getDate() - 90);
      break;
    case '1y':
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    default:
      startDate.setDate(startDate.getDate() - 30);
  }

  return this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$transactionType',
        totalPoints: { $sum: '$pointsChange' },
        count: { $sum: 1 },
        avgPoints: { $avg: '$pointsChange' }
      }
    }
  ]);
};

// Static method to create point history entry
pointHistorySchema.statics.createEntry = async function(data) {
  const {
    userId,
    transactionType,
    pointsChange,
    pointsBalance,
    source,
    sourceId,
    sourceModel,
    description,
    metadata = {}
  } = data;

  const entry = new this({
    userId,
    transactionType,
    pointsChange,
    pointsBalance,
    source,
    sourceId,
    sourceModel,
    description,
    metadata
  });

  return await entry.save();
};

export default mongoose.models.PointHistory || mongoose.model('PointHistory', pointHistorySchema);
