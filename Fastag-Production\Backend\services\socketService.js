import jwt from 'jsonwebtoken';

class SocketService {
  constructor(io) {
    this.io = io;
    this.userSockets = new Map(); // Map to store user ID -> socket ID mapping
    this.setupSocketHandlers();
  }

  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      console.log('🔌 New socket connection:', socket.id);

      // Handle user authentication
      socket.on('authenticate', async (token) => {
        try {
          if (!token) {
            socket.emit('auth_error', { message: 'No token provided' });
            return;
          }

          // Verify JWT token
          const decoded = jwt.verify(token, process.env.JWT_SECRET);
          const userId = decoded.userId;

          // Store user-socket mapping
          this.userSockets.set(userId, socket.id);
          socket.userId = userId;

          console.log(`✅ User ${userId} authenticated with socket ${socket.id}`);
          socket.emit('authenticated', { userId, message: 'Successfully authenticated' });

          // Join user to their personal room for targeted updates
          socket.join(`user_${userId}`);
          console.log(`👤 User ${userId} joined personal room`);

        } catch (error) {
          console.error('❌ Socket authentication error:', error);
          socket.emit('auth_error', { message: 'Invalid token' });
        }
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        if (socket.userId) {
          this.userSockets.delete(socket.userId);
          console.log(`🔌 User ${socket.userId} disconnected from socket ${socket.id}`);
        } else {
          console.log(`🔌 Anonymous socket ${socket.id} disconnected`);
        }
      });

      // Handle ping for connection health check
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: Date.now() });
      });
    });
  }

  // Emit points update to specific user
  emitPointsUpdate(userId, pointsData) {
    try {
      const socketId = this.userSockets.get(userId);
      if (socketId) {
        console.log(`📊 Emitting points update to user ${userId}:`, pointsData);
        this.io.to(`user_${userId}`).emit('points_updated', {
          monthlyPoints: pointsData.monthlyPoints,
          yearlyPoints: pointsData.yearlyPoints,
          timestamp: Date.now()
        });
        return true;
      } else {
        console.log(`⚠️ User ${userId} not connected via socket`);
        return false;
      }
    } catch (error) {
      console.error('❌ Error emitting points update:', error);
      return false;
    }
  }

  // Emit notification to specific user
  emitNotification(userId, notification) {
    try {
      const socketId = this.userSockets.get(userId);
      if (socketId) {
        console.log(`🔔 Emitting notification to user ${userId}:`, notification);
        this.io.to(`user_${userId}`).emit('new_notification', {
          ...notification,
          timestamp: Date.now()
        });
        return true;
      } else {
        console.log(`⚠️ User ${userId} not connected via socket`);
        return false;
      }
    } catch (error) {
      console.error('❌ Error emitting notification:', error);
      return false;
    }
  }

  // Broadcast to all connected users
  broadcastToAll(event, data) {
    try {
      console.log(`📢 Broadcasting ${event} to all users:`, data);
      this.io.emit(event, {
        ...data,
        timestamp: Date.now()
      });
      return true;
    } catch (error) {
      console.error('❌ Error broadcasting to all users:', error);
      return false;
    }
  }

  // Get connected users count
  getConnectedUsersCount() {
    return this.userSockets.size;
  }

  // Get all connected user IDs
  getConnectedUserIds() {
    return Array.from(this.userSockets.keys());
  }

  // Check if user is connected
  isUserConnected(userId) {
    return this.userSockets.has(userId);
  }
}

export default SocketService;
